
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#f8f8f8","background":"#efeff4","navigationBar":{"backgroundColor":"#007AFF","titleText":"本机相册","type":"default","titleColor":"#ffffff"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"备份助手","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.66","entryPagePath":"pages/gallery/gallery","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#7A7E83","selectedColor":"#007AFF","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index","iconPath":"/static/images/backup.png","selectedIconPath":"/static/images/backup-active.png","text":"备份助手"},{"pagePath":"pages/gallery/gallery","iconPath":"/static/images/gallery.png","selectedIconPath":"/static/images/gallery-active.png","text":"本机相册"},{"pagePath":"pages/webdav/webdav","text":"WebDAV云盘"}],"backgroundColor":"#F8F8F8","selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/gallery/gallery","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":1,"backgroundColor":"#f8f8f8","navigationBar":{"backgroundColor":"#007AFF","titleText":"本机相册","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/index/index","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"backgroundColor":"#f8f8f8","navigationBar":{"backgroundColor":"#007AFF","titleText":"备份助手","type":"default","titleColor":"#ffffff"},"isNVue":false}},{"path":"pages/webdav/webdav","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"backgroundColor":"#f8f8f8","navigationBar":{"backgroundColor":"#007AFF","titleText":"WebDAV云盘","type":"default","titleColor":"#ffffff"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  