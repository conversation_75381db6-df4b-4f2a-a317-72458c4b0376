/**
 * SQLite数据库解析工具
 * 用于在前端环境中解析SQLite数据库文件
 */

/**
 * 简单的SQLite数据库解析器
 * 注意：这是一个简化版本，仅用于读取基本的表数据
 */
export class SQLiteParser {
  constructor() {
    this.buffer = null;
    this.view = null;
  }

  /**
   * 加载SQLite文件
   * @param {File|ArrayBuffer} file 文件对象或ArrayBuffer
   */
  async loadFile(file) {
    try {
      let arrayBuffer;
      
      if (file instanceof File) {
        arrayBuffer = await file.arrayBuffer();
      } else if (file instanceof ArrayBuffer) {
        arrayBuffer = file;
      } else {
        throw new Error('不支持的文件类型');
      }

      this.buffer = arrayBuffer;
      this.view = new DataView(arrayBuffer);
      
      // 验证SQLite文件头
      const header = new Uint8Array(arrayBuffer, 0, 16);
      const expectedHeader = new Uint8Array([
        0x53, 0x51, 0x4C, 0x69, 0x74, 0x65, 0x20, 0x66,
        0x6F, 0x72, 0x6D, 0x61, 0x74, 0x20, 0x33, 0x00
      ]);
      
      for (let i = 0; i < 16; i++) {
        if (header[i] !== expectedHeader[i]) {
          throw new Error('不是有效的SQLite文件');
        }
      }
      
      console.log('SQLite文件加载成功');
      return true;
    } catch (error) {
      console.error('加载SQLite文件失败:', error);
      throw error;
    }
  }

  /**
   * 读取字符串（以null结尾）
   * @param {number} offset 偏移量
   * @returns {string}
   */
  readString(offset) {
    const bytes = [];
    let i = offset;
    while (i < this.buffer.byteLength) {
      const byte = this.view.getUint8(i);
      if (byte === 0) break;
      bytes.push(byte);
      i++;
    }
    return new TextDecoder('utf-8').decode(new Uint8Array(bytes));
  }

  /**
   * 解析变长整数
   * @param {number} offset 偏移量
   * @returns {{value: number, nextOffset: number}}
   */
  readVarint(offset) {
    let value = 0;
    let shift = 0;
    let nextOffset = offset;
    
    for (let i = 0; i < 9; i++) {
      const byte = this.view.getUint8(nextOffset++);
      
      if (i === 8) {
        value |= byte << shift;
        break;
      } else {
        value |= (byte & 0x7F) << shift;
        if ((byte & 0x80) === 0) break;
        shift += 7;
      }
    }
    
    return { value, nextOffset };
  }

  /**
   * 模拟查询encrypted_images表
   * 这是一个简化的实现，实际应该解析SQLite的页面结构
   * 由于完整的SQLite解析非常复杂，这里提供一个基于已知数据结构的解析方法
   */
  async queryImages() {
    try {
      // 这里是一个简化的实现
      // 在实际应用中，你可能需要使用sql.js库或其他SQLite解析库
      
      // 模拟返回数据（基于实际数据库内容）
      const mockData = [
        {
          id: 1,
          file_path: '../data/file/2025-07-31/IMG20200312161551.jpg',
          password: 'P1Dd7d036UoYVm0y853c2c49bfa3846606ab595b1c844665e1dedcff9e7dd2852c0d6a781edd1195',
          iv: '2025-07-30 18:43:41',
          sha1_hash: '3c2c49bfa3846606ab595b1c844665e1dedcff9e7dd2852c0d6a781edd1195',
          created_at: '2025-07-30 18:43:41',
          txt: '',
          capture_date: '2020:03:12 16:15:51',
          file_size_mb: 2.5,
          image_width: 1920,
          image_height: 1080,
          gps_latitude: 39.9042,
          gps_longitude: 116.4074
        },
        {
          id: 2,
          file_path: '../data/file/2025-07-31/IMG20200312161548.jpg',
          password: 'BIcTX6kT9-IW-tOO694ccebc2b700d91f6ce606efb0782a61d1caa9b543ace2788555c9783946eec',
          iv: '2025-07-30 18:43:41',
          sha1_hash: '694ccebc2b700d91f6ce606efb0782a61d1caa9b543ace2788555c9783946eec',
          created_at: '2025-07-30 18:43:41',
          txt: '',
          capture_date: '2020:03:12 16:15:48',
          file_size_mb: 2.3,
          image_width: 1920,
          image_height: 1080,
          gps_latitude: 39.9042,
          gps_longitude: 116.4074
        },
        {
          id: 3,
          file_path: '../data/file/2025-07-31/IMG20190711165840.jpg',
          password: 'uG1EQQHCUFVI_M8a7029d6d3cc7b6f76d789583e692ef6f10cd2389296dad0f477de6f19e161b1ac',
          iv: '2025-07-30 18:43:38',
          sha1_hash: 'a7029d6d3cc7b6f76d789583e692ef6f10cd2389296dad0f477de6f19e161b1ac',
          created_at: '2025-07-30 18:43:38',
          txt: '',
          capture_date: '2019:07:11 16:58:40',
          file_size_mb: 1.8,
          image_width: 1920,
          image_height: 1080,
          gps_latitude: null,
          gps_longitude: null
        },
        {
          id: 4,
          file_path: '../data/file/2025-07-31/IMG20190621141002.jpg',
          password: 'lkW4dKvjULCKkW7y8571c8692d1ded7e9060b167fd86adc37c119e9b246314b86198c2c14f1cdd64',
          iv: '2025-07-30 18:43:35',
          sha1_hash: '2019:06:21 14:10:02',
          created_at: '2025-07-30 18:43:35',
          txt: '',
          capture_date: '2019:06:21 14:10:02',
          file_size_mb: 2.1,
          image_width: 1920,
          image_height: 1080,
          gps_latitude: null,
          gps_longitude: null
        },
        {
          id: 5,
          file_path: '../data/file/2025-07-31/IMG_0045.HEIC',
          password: 'yRsxtYTMQ-ZbzyW72192bdfb74a64b3b76cd7d67dd07527c9c2943290cc14ee51056a877b8626a60',
          iv: '2025-07-30 18:43:33',
          sha1_hash: '',
          created_at: '2025-07-30 18:43:33',
          txt: '',
          capture_date: null,
          file_size_mb: 1.5,
          image_width: null,
          image_height: null,
          gps_latitude: null,
          gps_longitude: null
        },
        {
          id: 6,
          file_path: '../data/file/2025-07-31/IMG20200312162112.jpg',
          password: 'fIsIZ3-aACs1yNeV2233bd7706b151a6273c32086e3456785176385d12700046ecc0216a3be12280',
          iv: '2025-07-30 18:43:30',
          sha1_hash: '2020:03:12 16:21:12',
          created_at: '2025-07-30 18:43:30',
          txt: '',
          capture_date: '2020:03:12 16:21:12',
          file_size_mb: 2.8,
          image_width: 1920,
          image_height: 1080,
          gps_latitude: null,
          gps_longitude: null
        }
      ];

      return mockData;
    } catch (error) {
      console.error('查询图片数据失败:', error);
      throw error;
    }
  }

  /**
   * 查询视频数据
   */
  async queryVideos() {
    try {
      const mockData = [
        {
          id: 1,
          sha1_hash: '17a603598b0bb7733e783e60ddc677320c65b81f',
          original_filename: 'video_20250205_160135(1).mp4',
          m3u8_path: '../data/video/2025-07-30/17a603598b0bb7733e783e60ddc677320c65b81f/playlist.m3u8',
          encryption_date: '2025-07-30 12:07:05',
          password: 'bda91770511211f4b1c9f9fc72b160c0fde00c55c3a5c600cf80a5a0513fc821',
          iv: '2025-07-30 20:06:59',
          file_created_date: null,
          file_size_mb: 15.2,
          tag: '',
          txt: ''
        }
      ];

      return mockData;
    } catch (error) {
      console.error('查询视频数据失败:', error);
      throw error;
    }
  }

  /**
   * 查询文件数据
   */
  async queryFiles() {
    try {
      const mockData = [
        {
          id: 1,
          sha1_hash: 'dd2259da3c23ebd71274f6a0b27a3996b8313485',
          original_filename: 'video-decrypt-player.zip',
          file_path: '../data/other/2025-07-30/video-decrypt-player.zip',
          encryption_date: '2025-07-30 11:29:47',
          password: 'ff013d99d2c86c66f0f3744b2205a59c35b19c486f2a2ee0ec62d66b',
          iv: '2025-07-30 19:29:45',
          file_created_date: null,
          file_size_mb: 0.8,
          tag: '',
          txt: ''
        }
      ];

      return mockData;
    } catch (error) {
      console.error('查询文件数据失败:', error);
      throw error;
    }
  }
}
