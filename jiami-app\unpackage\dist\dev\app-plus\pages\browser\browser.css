
.container[data-v-0c8400ea] {
  flex: 1;
  background-color: #f8f8f8;
}
.tab-bar[data-v-0c8400ea] {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
}
.tab-item[data-v-0c8400ea] {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-size: 16px;
  color: #666;
  border-bottom: 2px solid transparent;
}
.tab-item.active[data-v-0c8400ea] {
  color: #007AFF;
  border-bottom-color: #007AFF;
}
.db-selector[data-v-0c8400ea] {
  padding: 40px 20px;
  text-align: center;
}
.parser-option[data-v-0c8400ea] {
  margin-bottom: 20px;
  text-align: left;
  display: inline-block;
}
.parser-option uni-label[data-v-0c8400ea] {
  display: block;
  margin: 10px 0;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}
.parser-option uni-input[type="radio"][data-v-0c8400ea] {
  margin-right: 8px;
}
.select-btn[data-v-0c8400ea] {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  margin-bottom: 10px;
}
.tip[data-v-0c8400ea] {
  color: #999;
  font-size: 14px;
  display: block;
  margin: 5px 0;
}
.settings[data-v-0c8400ea] {
  padding: 20px;
  background-color: #fff;
  margin: 10px;
  border-radius: 8px;
}
.setting-item[data-v-0c8400ea] {
  margin-bottom: 15px;
}
.label[data-v-0c8400ea] {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}
.input[data-v-0c8400ea] {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}
.save-btn[data-v-0c8400ea] {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  width: 100%;
}
.content[data-v-0c8400ea] {
  flex: 1;
  padding: 10px;
}
.image-grid[data-v-0c8400ea] {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
.image-item[data-v-0c8400ea] {
  width: calc(50% - 5px);
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.thumbnail-container[data-v-0c8400ea] {
  position: relative;
  width: 100%;
  height: 150px;
}
.thumbnail[data-v-0c8400ea] {
  width: 100%;
  height: 100%;
}
.loading-overlay[data-v-0c8400ea] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-text[data-v-0c8400ea] {
  color: white;
  font-size: 12px;
}
.image-info[data-v-0c8400ea] {
  padding: 10px;
}
.filename[data-v-0c8400ea] {
  font-size: 12px;
  color: #333;
  display: block;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.date[data-v-0c8400ea] {
  font-size: 11px;
  color: #999;
  margin-bottom: 3px;
}
.size[data-v-0c8400ea] {
  font-size: 11px;
  color: #666;
}
.video-list[data-v-0c8400ea], .file-list[data-v-0c8400ea] {
  background-color: #fff;
  border-radius: 8px;
}
.video-item[data-v-0c8400ea], .file-item[data-v-0c8400ea] {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}
.video-item[data-v-0c8400ea]:last-child, .file-item[data-v-0c8400ea]:last-child {
  border-bottom: none;
}
.video-thumbnail[data-v-0c8400ea] {
  width: 60px;
  height: 60px;
  background-color: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}
.play-icon[data-v-0c8400ea] {
  font-size: 24px;
  color: #007AFF;
}
.file-icon[data-v-0c8400ea] {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  margin-right: 15px;
}
.video-info[data-v-0c8400ea], .file-info[data-v-0c8400ea] {
  flex: 1;
}
.video-info .filename[data-v-0c8400ea], .file-info .filename[data-v-0c8400ea] {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}
.video-info .date[data-v-0c8400ea], .file-info .date[data-v-0c8400ea] {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}
.video-info .size[data-v-0c8400ea], .file-info .size[data-v-0c8400ea] {
  font-size: 12px;
  color: #666;
}
.loading[data-v-0c8400ea], .empty[data-v-0c8400ea] {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}
