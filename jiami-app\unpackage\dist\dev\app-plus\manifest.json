{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__BACKUP_APP", "name": "备份助手", "version": {"name": "1.0.0", "code": "100"}, "description": "照片视频文件备份应用", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Gallery": {"description": "相册模块"}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "<uses-permission android:name=\"android.permission.VIBRATE\" />", "<uses-permission android:name=\"android.permission.READ_LOGS\" />", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />", "<uses-feature android:name=\"android.hardware.camera.autofocus\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.CAMERA\" />", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />", "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />", "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />", "<uses-feature android:name=\"android.hardware.camera\" />", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\" />", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />", "<uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\" />", "<uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\" />", "<uses-permission android:name=\"android.permission.READ_MEDIA_AUDIO\" />", "<uses-permission android:name=\"android.permission.MANAGE_EXTERNAL_STORAGE\" tools:ignore=\"ScopedStorage\" />"], "targetSdkVersion": 33, "minSdkVersion": 21}, "apple": {}, "plugins": {"audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#007AFF"}, "arguments": "{\"name\":\"\",\"path\":\"\",\"query\":\"\"}", "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#F8F8F8", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.66", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#7A7E83", "selectedColor": "#007AFF", "borderStyle": "rgba(0,0,0,0.4)", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "50px", "list": [{"pagePath": "pages/index/index", "iconPath": "/static/images/backup.png", "selectedIconPath": "/static/images/backup-active.png", "text": "备份助手"}, {"pagePath": "pages/gallery/gallery", "iconPath": "/static/images/gallery.png", "selectedIconPath": "/static/images/gallery-active.png", "text": "本机相册"}, {"pagePath": "pages/webdav/webdav", "text": "WebDAV云盘"}, {"pagePath": "pages/browser/browser", "text": "文件浏览器"}], "backgroundColor": "#F8F8F8", "selectedIndex": 0, "shown": true, "child": ["lauchwebview"], "selected": 1}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}, "safearea": {"background": "#F8F8F8", "bottom": {"offset": "auto"}}}, "launch_path": "__uniappview.html"}