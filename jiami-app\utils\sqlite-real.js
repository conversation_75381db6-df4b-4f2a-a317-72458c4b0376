/**
 * 真实的SQLite数据库解析器
 * 使用sql.js库在浏览器中解析SQLite数据库
 * 
 * 使用前需要引入sql.js库：
 * <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>
 */

export class SQLiteRealParser {
  constructor() {
    this.db = null;
    this.SQL = null;
  }

  /**
   * 初始化sql.js库
   */
  async initSQL() {
    if (this.SQL) return;
    
    try {
      // 检查是否已加载sql.js
      if (typeof window.initSqlJs === 'undefined') {
        throw new Error('sql.js库未加载，请先引入sql.js');
      }
      
      this.SQL = await window.initSqlJs({
        // 可以指定wasm文件的路径
        locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
      });
      
      console.log('sql.js初始化成功');
    } catch (error) {
      console.error('sql.js初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载SQLite文件
   * @param {File|ArrayBuffer} file 文件对象或ArrayBuffer
   */
  async loadFile(file) {
    try {
      await this.initSQL();
      
      let arrayBuffer;
      
      if (file instanceof File) {
        arrayBuffer = await file.arrayBuffer();
      } else if (file instanceof ArrayBuffer) {
        arrayBuffer = file;
      } else {
        throw new Error('不支持的文件类型');
      }

      // 创建数据库实例
      const uint8Array = new Uint8Array(arrayBuffer);
      this.db = new this.SQL.Database(uint8Array);
      
      console.log('SQLite数据库加载成功');
      
      // 验证数据库结构
      await this.validateDatabase();
      
      return true;
    } catch (error) {
      console.error('加载SQLite文件失败:', error);
      throw error;
    }
  }

  /**
   * 验证数据库结构
   */
  async validateDatabase() {
    try {
      // 检查必要的表是否存在
      const tables = this.db.exec("SELECT name FROM sqlite_master WHERE type='table'");
      const tableNames = tables[0] ? tables[0].values.map(row => row[0]) : [];
      
      const requiredTables = ['encrypted_images', 'encrypted_videos', 'encrypted_files'];
      const missingTables = requiredTables.filter(table => !tableNames.includes(table));
      
      if (missingTables.length > 0) {
        console.warn('缺少表:', missingTables);
      }
      
      console.log('数据库表:', tableNames);
    } catch (error) {
      console.error('验证数据库结构失败:', error);
    }
  }

  /**
   * 执行SQL查询
   * @param {string} sql SQL查询语句
   * @returns {Array} 查询结果
   */
  query(sql) {
    if (!this.db) {
      throw new Error('数据库未加载');
    }
    
    try {
      const results = this.db.exec(sql);
      if (results.length === 0) {
        return [];
      }
      
      const columns = results[0].columns;
      const values = results[0].values;
      
      // 将结果转换为对象数组
      return values.map(row => {
        const obj = {};
        columns.forEach((col, index) => {
          obj[col] = row[index];
        });
        return obj;
      });
    } catch (error) {
      console.error('SQL查询失败:', error);
      throw error;
    }
  }

  /**
   * 查询图片数据
   */
  async queryImages() {
    try {
      const sql = `
        SELECT 
          id, file_path, password, iv, sha1_hash, created_at, 
          txt, capture_date, file_size_mb, image_width, image_height,
          gps_latitude, gps_longitude
        FROM encrypted_images 
        ORDER BY created_at DESC
      `;
      
      return this.query(sql);
    } catch (error) {
      console.error('查询图片数据失败:', error);
      throw error;
    }
  }

  /**
   * 查询视频数据
   */
  async queryVideos() {
    try {
      const sql = `
        SELECT 
          id, sha1_hash, original_filename, m3u8_path, encryption_date,
          password, iv, file_created_date, file_size_mb, tag, txt
        FROM encrypted_videos 
        ORDER BY encryption_date DESC
      `;
      
      return this.query(sql);
    } catch (error) {
      console.error('查询视频数据失败:', error);
      throw error;
    }
  }

  /**
   * 查询文件数据
   */
  async queryFiles() {
    try {
      const sql = `
        SELECT 
          id, sha1_hash, original_filename, file_path, encryption_date,
          password, iv, file_created_date, file_size_mb, tag, txt
        FROM encrypted_files 
        ORDER BY encryption_date DESC
      `;
      
      return this.query(sql);
    } catch (error) {
      console.error('查询文件数据失败:', error);
      throw error;
    }
  }

  /**
   * 根据条件查询图片
   * @param {Object} conditions 查询条件
   */
  async queryImagesWithConditions(conditions = {}) {
    try {
      let sql = `
        SELECT 
          id, file_path, password, iv, sha1_hash, created_at, 
          txt, capture_date, file_size_mb, image_width, image_height,
          gps_latitude, gps_longitude
        FROM encrypted_images 
      `;
      
      const whereClauses = [];
      const params = [];
      
      if (conditions.dateFrom) {
        whereClauses.push('created_at >= ?');
        params.push(conditions.dateFrom);
      }
      
      if (conditions.dateTo) {
        whereClauses.push('created_at <= ?');
        params.push(conditions.dateTo);
      }
      
      if (conditions.tag) {
        whereClauses.push('txt LIKE ?');
        params.push(`%${conditions.tag}%`);
      }
      
      if (whereClauses.length > 0) {
        sql += ' WHERE ' + whereClauses.join(' AND ');
      }
      
      sql += ' ORDER BY created_at DESC';
      
      if (conditions.limit) {
        sql += ` LIMIT ${conditions.limit}`;
      }
      
      return this.query(sql);
    } catch (error) {
      console.error('条件查询图片失败:', error);
      throw error;
    }
  }

  /**
   * 获取统计信息
   */
  async getStatistics() {
    try {
      const imageCount = this.query('SELECT COUNT(*) as count FROM encrypted_images')[0]?.count || 0;
      const videoCount = this.query('SELECT COUNT(*) as count FROM encrypted_videos')[0]?.count || 0;
      const fileCount = this.query('SELECT COUNT(*) as count FROM encrypted_files')[0]?.count || 0;
      
      const totalSize = this.query(`
        SELECT 
          (SELECT COALESCE(SUM(file_size_mb), 0) FROM encrypted_images) +
          (SELECT COALESCE(SUM(file_size_mb), 0) FROM encrypted_videos) +
          (SELECT COALESCE(SUM(file_size_mb), 0) FROM encrypted_files) as total_size
      `)[0]?.total_size || 0;
      
      return {
        imageCount,
        videoCount,
        fileCount,
        totalCount: imageCount + videoCount + fileCount,
        totalSizeMB: totalSize
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}
