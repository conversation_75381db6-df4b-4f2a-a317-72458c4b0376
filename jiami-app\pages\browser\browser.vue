<template>
  <view class="container">
    <!-- 顶部切换标签 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'images' }"
        @click="switchTab('images')"
      >
        图片
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'videos' }"
        @click="switchTab('videos')"
      >
        视频
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'files' }"
        @click="switchTab('files')"
      >
        文件
      </view>
    </view>

    <!-- 数据库文件选择 -->
    <view class="db-selector" v-if="!dbLoaded">
      <view class="parser-option">
        <label>
          <input type="radio" v-model="useRealParser" :value="false" />
          使用模拟数据（快速测试）
        </label>
        <label>
          <input type="radio" v-model="useRealParser" :value="true" />
          使用真实SQLite解析器
        </label>
      </view>

      <button @click="selectDatabase" class="select-btn" :disabled="loading">
        {{ loading ? '加载中...' : '选择数据库文件' }}
      </button>

      <view class="tips-container">
        <text class="tip">请选择doc文件夹下的image_encryption.db文件</text>
        <text class="tip" v-if="useRealParser">注意：真实解析器需要加载sql.js库，首次使用可能较慢</text>

        <!-- 环境提示 -->
        <view class="env-tips">
          <text class="tip-title">文件选择说明：</text>
          <text class="tip">• Web环境：点击按钮直接选择文件</text>
          <text class="tip">• App环境：将数据库文件放在Documents目录</text>
          <text class="tip">• 支持格式：.db, .sqlite, .sqlite3</text>
        </view>
      </view>
    </view>

    <!-- 设置区域 -->
    <view class="settings" v-if="dbLoaded && !settingsConfigured">
      <view class="setting-item">
        <text class="label">服务器域名:</text>
        <input v-model="serverDomain" placeholder="http://example.com" class="input" />
      </view>
      <view class="setting-item">
        <text class="label">访问密钥:</text>
        <input v-model="accessKey" placeholder="访问密钥" class="input" />
      </view>
      <button @click="saveSettings" class="save-btn">保存设置</button>
    </view>

    <!-- 内容区域 -->
    <view class="content" v-if="settingsConfigured">
      <!-- 图片列表 -->
      <view v-if="activeTab === 'images'" class="image-grid">
        <view
          v-for="(image, index) in imageList"
          :key="image.id"
          class="image-item"
          @click="previewImage(image, index)"
        >
          <view class="thumbnail-container">
            <image
              :src="image.thumbnailUrl"
              class="thumbnail"
              mode="aspectFill"
              @error="onImageError"
              @load="onImageLoad"
            />
            <view class="loading-overlay" v-if="image.loading">
              <text class="loading-text">加载中...</text>
            </view>
          </view>
          <view class="image-info">
            <text class="filename">{{ image.original_filename || '未知文件名' }}</text>
            <text class="date">{{ formatDate(image.created_at) }}</text>
            <text class="size">{{ image.file_size_mb }}MB</text>
          </view>
        </view>
      </view>

      <!-- 视频列表 -->
      <view v-if="activeTab === 'videos'" class="video-list">
        <view
          v-for="video in videoList"
          :key="video.id"
          class="video-item"
          @click="playVideo(video)"
        >
          <view class="video-thumbnail">
            <text class="play-icon">▶</text>
          </view>
          <view class="video-info">
            <text class="filename">{{ video.displayName }}</text>
            <text class="date">{{ formatDate(video.encryption_date) }}</text>
            <text class="size">{{ video.file_size_mb }}MB</text>
          </view>
        </view>
      </view>

      <!-- 文件列表 -->
      <view v-if="activeTab === 'files'" class="file-list">
        <view
          v-for="file in fileList"
          :key="file.id"
          class="file-item"
          @click="downloadFile(file)"
        >
          <view class="file-icon">📄</view>
          <view class="file-info">
            <text class="filename">{{ file.displayName }}</text>
            <text class="date">{{ formatDate(file.encryption_date) }}</text>
            <text class="size">{{ file.file_size_mb }}MB</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && getCurrentList().length === 0" class="empty">
        <text>暂无数据</text>
      </view>
    </view>
  </view>
</template>

<script>
import { SQLiteParser } from '../../utils/sqlite-parser.js'
import { SQLiteRealParser } from '../../utils/sqlite-real.js'
import {
  generateSHA256,
  decryptAES256GCM,
  createBlobUrl,
  getMimeType,
  generateFileUrl,
  generateThumbnailUrl
} from '../../utils/crypto-utils.js'

export default {
  data() {
    return {
      activeTab: 'images',
      dbLoaded: false,
      settingsConfigured: false,
      serverDomain: '',
      accessKey: '',
      imageList: [],
      videoList: [],
      fileList: [],
      loading: false,
      dbParser: null,
      decryptedImageCache: new Map(), // 缓存解密后的图片
      useRealParser: false // 是否使用真实的SQLite解析器
    }
  },
  
  onLoad() {
    this.loadSettings();
  },
  
  methods: {
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab;
      if (this.settingsConfigured && this.dbLoaded) {
        this.loadData();
      }
    },

    // 选择数据库文件
    async selectDatabase() {
      try {
        // 检查运行环境
        const systemInfo = uni.getSystemInfoSync();
        console.log('系统信息:', systemInfo);

        // 在H5/Web环境中使用input file选择
        if (systemInfo.platform === 'devtools' ||
            systemInfo.uniPlatform === 'web' ||
            typeof document !== 'undefined') {
          this.selectFileForWeb();
        } else {
          // 在App环境中尝试不同的文件选择方法
          await this.selectFileForApp();
        }
      } catch (error) {
        console.error('选择文件失败:', error);
        uni.showToast({
          title: `选择文件失败: ${error.message}`,
          icon: 'none'
        });
      }
    },

    // Web环境文件选择
    selectFileForWeb() {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.db,.sqlite,.sqlite3';
      input.style.display = 'none';

      input.onchange = async (e) => {
        const file = e.target.files[0];
        if (file) {
          console.log('选择的文件:', file.name, file.size);
          await this.loadDatabase(file);
        }
        // 清理DOM
        document.body.removeChild(input);
      };

      input.oncancel = () => {
        document.body.removeChild(input);
      };

      document.body.appendChild(input);
      input.click();
    },

    // App环境文件选择
    async selectFileForApp() {
      try {
        // 方法1: 尝试使用uni.chooseFile
        if (typeof uni.chooseFile === 'function') {
          const res = await uni.chooseFile({
            count: 1,
            extension: ['.db', '.sqlite', '.sqlite3']
          });

          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            await this.loadDatabaseFromPath(res.tempFilePaths[0]);
            return;
          }
        }

        // 方法2: 尝试使用plus.io (5+App环境)
        if (typeof plus !== 'undefined' && plus.io) {
          this.selectFileWithPlus();
          return;
        }

        // 方法3: 提示用户手动操作
        uni.showModal({
          title: '文件选择',
          content: '当前环境不支持直接选择文件，请将数据库文件放置在应用目录下，或使用Web版本',
          showCancel: false
        });

      } catch (error) {
        console.error('App环境文件选择失败:', error);
        throw error;
      }
    },

    // 使用5+App的文件选择
    selectFileWithPlus() {
      try {
        plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS, (fs) => {
          fs.root.createReader().readEntries((entries) => {
            const dbFiles = entries.filter(entry =>
              entry.name.endsWith('.db') ||
              entry.name.endsWith('.sqlite') ||
              entry.name.endsWith('.sqlite3')
            );

            if (dbFiles.length === 0) {
              uni.showToast({
                title: '未找到数据库文件',
                icon: 'none'
              });
              return;
            }

            // 如果只有一个文件，直接使用
            if (dbFiles.length === 1) {
              this.loadDatabaseFromPath(dbFiles[0].fullPath);
              return;
            }

            // 多个文件时让用户选择
            const items = dbFiles.map(file => file.name);
            uni.showActionSheet({
              itemList: items,
              success: (res) => {
                const selectedFile = dbFiles[res.tapIndex];
                this.loadDatabaseFromPath(selectedFile.fullPath);
              }
            });
          });
        });
      } catch (error) {
        console.error('Plus文件选择失败:', error);
        throw error;
      }
    },

    // 从文件路径加载数据库
    async loadDatabaseFromPath(filePath) {
      try {
        // 读取文件内容
        const fileContent = await this.readFileFromPath(filePath);
        await this.loadDatabase(fileContent);
      } catch (error) {
        console.error('从路径加载数据库失败:', error);
        throw error;
      }
    },

    // 读取文件内容
    async readFileFromPath(filePath) {
      return new Promise((resolve, reject) => {
        // 在5+App环境中读取文件
        if (typeof plus !== 'undefined' && plus.io) {
          plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
            entry.file((file) => {
              const reader = new plus.io.FileReader();
              reader.onloadend = (e) => {
                resolve(e.target.result);
              };
              reader.onerror = reject;
              reader.readAsArrayBuffer(file);
            }, reject);
          }, reject);
        } else {
          reject(new Error('不支持的文件读取环境'));
        }
      });
    },

    // 加载数据库
    async loadDatabase(file) {
      try {
        this.loading = true;
        await this.parseSQLiteFile(file);
        this.dbLoaded = true;

        if (this.settingsConfigured) {
          await this.loadData();
        }

        uni.showToast({
          title: '数据库加载成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('加载数据库失败:', error);
        uni.showToast({
          title: '加载数据库失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 解析SQLite文件
    async parseSQLiteFile(file) {
      try {
        if (this.useRealParser) {
          this.dbParser = new SQLiteRealParser();
        } else {
          this.dbParser = new SQLiteParser();
        }

        await this.dbParser.loadFile(file);
        console.log('SQLite文件解析成功，使用', this.useRealParser ? '真实' : '模拟', '解析器');
      } catch (error) {
        console.error('解析SQLite文件失败:', error);
        throw error;
      }
    },

    // 加载设置
    loadSettings() {
      try {
        const domain = uni.getStorageSync('serverDomain');
        const key = uni.getStorageSync('accessKey');
        
        if (domain && key) {
          this.serverDomain = domain;
          this.accessKey = key;
          this.settingsConfigured = true;
        }
      } catch (error) {
        console.error('加载设置失败:', error);
      }
    },

    // 保存设置
    saveSettings() {
      if (!this.serverDomain || !this.accessKey) {
        uni.showToast({
          title: '请填写完整设置',
          icon: 'none'
        });
        return;
      }

      try {
        uni.setStorageSync('serverDomain', this.serverDomain);
        uni.setStorageSync('accessKey', this.accessKey);
        this.settingsConfigured = true;
        
        if (this.dbLoaded) {
          this.loadData();
        }
        
        uni.showToast({
          title: '设置保存成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('保存设置失败:', error);
        uni.showToast({
          title: '保存设置失败',
          icon: 'none'
        });
      }
    },

    // 加载数据
    async loadData() {
      if (!this.dbParser) return;

      this.loading = true;
      try {
        switch (this.activeTab) {
          case 'images':
            await this.loadImages();
            break;
          case 'videos':
            await this.loadVideos();
            break;
          case 'files':
            await this.loadFiles();
            break;
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载图片数据
    async loadImages() {
      try {
        const images = await this.dbParser.queryImages();
        this.imageList = [];

        for (const image of images) {
          try {
            // 添加到列表，先显示占位符
            const imageItem = {
              ...image,
              thumbnailUrl: '', // 先设为空，后续异步加载
              original_filename: this.getFilenameFromPath(image.file_path),
              loading: true
            };

            this.imageList.push(imageItem);

            // 异步加载缩略图
            this.loadThumbnail(imageItem);
          } catch (error) {
            console.error('处理图片失败:', image.file_path, error);
          }
        }

        console.log('图片列表加载完成:', this.imageList.length);
      } catch (error) {
        console.error('加载图片数据失败:', error);
        throw error;
      }
    },

    // 加载缩略图
    async loadThumbnail(imageItem) {
      try {
        // 生成缩略图URL
        const thumbnailUrl = await generateThumbnailUrl(
          imageItem.file_path,
          this.serverDomain,
          this.accessKey
        );

        // 下载并解密缩略图
        const response = await fetch(thumbnailUrl);
        if (!response.ok) {
          throw new Error(`下载缩略图失败: ${response.status}`);
        }

        const encryptedData = await response.arrayBuffer();
        const encryptedHex = Array.from(new Uint8Array(encryptedData))
          .map(b => b.toString(16).padStart(2, '0')).join('');

        // 解密缩略图数据
        const decryptedData = await decryptAES256GCM(
          encryptedHex,
          imageItem.password,
          imageItem.iv
        );

        // 创建Blob URL
        const mimeType = getMimeType(imageItem.file_path);
        const blobUrl = createBlobUrl(decryptedData, mimeType);

        // 更新图片项
        imageItem.thumbnailUrl = blobUrl;
        imageItem.loading = false;
      } catch (error) {
        console.error('加载缩略图失败:', imageItem.file_path, error);
        imageItem.loading = false;
        imageItem.thumbnailUrl = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Yqg6L295aSx6LSlPC90ZXh0Pjwvc3ZnPg=='; // 错误占位符SVG
      }
    },

    // 加载视频数据
    async loadVideos() {
      try {
        const videos = await this.dbParser.queryVideos();
        this.videoList = videos.map(video => ({
          ...video,
          displayName: video.original_filename || this.getFilenameFromPath(video.m3u8_path)
        }));
        console.log('视频列表加载完成:', this.videoList.length);
      } catch (error) {
        console.error('加载视频数据失败:', error);
        throw error;
      }
    },

    // 加载文件数据
    async loadFiles() {
      try {
        const files = await this.dbParser.queryFiles();
        this.fileList = files.map(file => ({
          ...file,
          displayName: file.original_filename || this.getFilenameFromPath(file.file_path)
        }));
        console.log('文件列表加载完成:', this.fileList.length);
      } catch (error) {
        console.error('加载文件数据失败:', error);
        throw error;
      }
    },

    // 从文件路径中提取文件名
    getFilenameFromPath(filePath) {
      if (!filePath) return '';
      const parts = filePath.split('/');
      return parts[parts.length - 1];
    },

    // 下载并解密图片
    async downloadAndDecryptImage(image) {
      try {
        const cacheKey = image.file_path;

        // 检查缓存
        if (this.decryptedImageCache.has(cacheKey)) {
          return this.decryptedImageCache.get(cacheKey);
        }

        // 生成完整的图片URL
        const imageUrl = await generateFileUrl(
          image.file_path,
          this.serverDomain,
          this.accessKey,
          'file'
        );

        // 下载加密的图片数据
        const response = await fetch(imageUrl);
        if (!response.ok) {
          throw new Error(`下载失败: ${response.status}`);
        }

        const encryptedData = await response.arrayBuffer();
        const encryptedHex = Array.from(new Uint8Array(encryptedData))
          .map(b => b.toString(16).padStart(2, '0')).join('');

        // 解密图片数据
        const decryptedData = await decryptAES256GCM(
          encryptedHex,
          image.password,
          image.iv
        );

        // 创建Blob URL
        const mimeType = getMimeType(image.file_path);
        const blobUrl = createBlobUrl(decryptedData, mimeType);

        // 缓存结果
        this.decryptedImageCache.set(cacheKey, blobUrl);

        return blobUrl;
      } catch (error) {
        console.error('下载解密图片失败:', error);
        throw error;
      }
    },

    // 获取当前列表
    getCurrentList() {
      switch (this.activeTab) {
        case 'images': return this.imageList;
        case 'videos': return this.videoList;
        case 'files': return this.fileList;
        default: return [];
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString();
    },

    // 图片预览
    async previewImage(image, index) {
      try {
        uni.showLoading({
          title: '加载中...'
        });

        // 下载并解密图片
        const decryptedUrl = await this.downloadAndDecryptImage(image);

        // 预览图片
        uni.previewImage({
          urls: [decryptedUrl],
          current: 0
        });
      } catch (error) {
        console.error('预览图片失败:', error);
        uni.showToast({
          title: '预览失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 播放视频
    async playVideo(video) {
      try {
        uni.showToast({
          title: '视频播放功能开发中',
          icon: 'none'
        });
        console.log('播放视频:', video);
      } catch (error) {
        console.error('播放视频失败:', error);
      }
    },

    // 下载文件
    async downloadFile(file) {
      try {
        uni.showToast({
          title: '文件下载功能开发中',
          icon: 'none'
        });
        console.log('下载文件:', file);
      } catch (error) {
        console.error('下载文件失败:', error);
      }
    },

    // 图片加载错误
    onImageError(e) {
      console.error('图片加载失败:', e);
    },

    // 图片加载成功
    onImageLoad(e) {
      console.log('图片加载成功:', e);
    }
  }
}
</script>

<style scoped>
.container {
  flex: 1;
  background-color: #f8f8f8;
}

.tab-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-size: 16px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #007AFF;
  border-bottom-color: #007AFF;
}

.db-selector {
  padding: 40px 20px;
  text-align: center;
}

.parser-option {
  margin-bottom: 20px;
  text-align: left;
  display: inline-block;
}

.parser-option label {
  display: block;
  margin: 10px 0;
  font-size: 14px;
  color: #333;
  cursor: pointer;
}

.parser-option input[type="radio"] {
  margin-right: 8px;
}

.select-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  margin-bottom: 10px;
}

.tips-container {
  margin-top: 15px;
}

.tip {
  color: #999;
  font-size: 14px;
  display: block;
  margin: 5px 0;
}

.env-tips {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border-left: 3px solid #007AFF;
}

.tip-title {
  color: #333;
  font-size: 14px;
  font-weight: bold;
  display: block;
  margin-bottom: 8px;
}

.select-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.settings {
  padding: 20px;
  background-color: #fff;
  margin: 10px;
  border-radius: 8px;
}

.setting-item {
  margin-bottom: 15px;
}

.label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}

.input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.save-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  width: 100%;
}

.content {
  flex: 1;
  padding: 10px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  width: calc(50% - 5px);
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.thumbnail-container {
  position: relative;
  width: 100%;
  height: 150px;
}

.thumbnail {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text {
  color: white;
  font-size: 12px;
}

.image-info {
  padding: 10px;
}

.filename {
  font-size: 12px;
  color: #333;
  display: block;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.date {
  font-size: 11px;
  color: #999;
  margin-bottom: 3px;
}

.size {
  font-size: 11px;
  color: #666;
}

.video-list, .file-list {
  background-color: #fff;
  border-radius: 8px;
}

.video-item, .file-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.video-item:last-child, .file-item:last-child {
  border-bottom: none;
}

.video-thumbnail {
  width: 60px;
  height: 60px;
  background-color: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.play-icon {
  font-size: 24px;
  color: #007AFF;
}

.file-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  margin-right: 15px;
}

.video-info, .file-info {
  flex: 1;
}

.video-info .filename, .file-info .filename {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.video-info .date, .file-info .date {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

.video-info .size, .file-info .size {
  font-size: 12px;
  color: #666;
}

.loading, .empty {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}
</style>
