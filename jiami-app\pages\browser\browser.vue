<template>
  <view class="container">
    <!-- 顶部切换标签 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'images' }"
        @click="switchTab('images')"
      >
        图片
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'videos' }"
        @click="switchTab('videos')"
      >
        视频
      </view>
      <view 
        class="tab-item" 
        :class="{ active: activeTab === 'files' }"
        @click="switchTab('files')"
      >
        文件
      </view>
    </view>

    <!-- 数据库文件选择 -->
    <view class="db-selector" v-if="!dbLoaded">
      <button @click="selectDatabase" class="select-btn">选择数据库文件</button>
      <text class="tip">请选择doc文件夹下的image_encryption.db文件</text>
    </view>

    <!-- 设置区域 -->
    <view class="settings" v-if="dbLoaded && !settingsConfigured">
      <view class="setting-item">
        <text class="label">服务器域名:</text>
        <input v-model="serverDomain" placeholder="http://example.com" class="input" />
      </view>
      <view class="setting-item">
        <text class="label">访问密钥:</text>
        <input v-model="accessKey" placeholder="访问密钥" class="input" />
      </view>
      <button @click="saveSettings" class="save-btn">保存设置</button>
    </view>

    <!-- 内容区域 -->
    <view class="content" v-if="settingsConfigured">
      <!-- 图片列表 -->
      <view v-if="activeTab === 'images'" class="image-grid">
        <view 
          v-for="(image, index) in imageList" 
          :key="image.id"
          class="image-item"
          @click="previewImage(image, index)"
        >
          <image 
            :src="image.thumbnailUrl" 
            class="thumbnail"
            mode="aspectFill"
            @error="onImageError"
            @load="onImageLoad"
          />
          <view class="image-info">
            <text class="filename">{{ image.original_filename || '未知文件名' }}</text>
            <text class="date">{{ formatDate(image.created_at) }}</text>
          </view>
        </view>
      </view>

      <!-- 视频列表 -->
      <view v-if="activeTab === 'videos'" class="video-list">
        <view 
          v-for="video in videoList" 
          :key="video.id"
          class="video-item"
          @click="playVideo(video)"
        >
          <view class="video-thumbnail">
            <text class="play-icon">▶</text>
          </view>
          <view class="video-info">
            <text class="filename">{{ video.original_filename }}</text>
            <text class="date">{{ formatDate(video.encryption_date) }}</text>
            <text class="size">{{ video.file_size_mb }}MB</text>
          </view>
        </view>
      </view>

      <!-- 文件列表 -->
      <view v-if="activeTab === 'files'" class="file-list">
        <view 
          v-for="file in fileList" 
          :key="file.id"
          class="file-item"
          @click="downloadFile(file)"
        >
          <view class="file-icon">📄</view>
          <view class="file-info">
            <text class="filename">{{ file.original_filename }}</text>
            <text class="date">{{ formatDate(file.encryption_date) }}</text>
            <text class="size">{{ file.file_size_mb }}MB</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading">
        <text>加载中...</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && getCurrentList().length === 0" class="empty">
        <text>暂无数据</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeTab: 'images',
      dbLoaded: false,
      settingsConfigured: false,
      serverDomain: '',
      accessKey: '',
      imageList: [],
      videoList: [],
      fileList: [],
      loading: false,
      dbData: null
    }
  },
  
  onLoad() {
    this.loadSettings();
  },
  
  methods: {
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab;
      if (this.settingsConfigured && this.dbLoaded) {
        this.loadData();
      }
    },

    // 选择数据库文件
    async selectDatabase() {
      try {
        const res = await uni.chooseFile({
          count: 1,
          extension: ['.db']
        });
        
        if (res.tempFilePaths && res.tempFilePaths.length > 0) {
          await this.loadDatabase(res.tempFilePaths[0]);
        }
      } catch (error) {
        console.error('选择文件失败:', error);
        uni.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    },

    // 加载数据库
    async loadDatabase(filePath) {
      try {
        this.loading = true;
        // 这里需要实现SQLite数据库读取逻辑
        // 由于uniapp环境限制，可能需要使用插件或原生能力
        await this.parseSQLiteFile(filePath);
        this.dbLoaded = true;
        
        if (this.settingsConfigured) {
          await this.loadData();
        }
      } catch (error) {
        console.error('加载数据库失败:', error);
        uni.showToast({
          title: '加载数据库失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 解析SQLite文件 (需要实现)
    async parseSQLiteFile(filePath) {
      // TODO: 实现SQLite文件解析
      // 这里需要使用SQLite插件或原生能力来读取数据库
      console.log('解析SQLite文件:', filePath);
    },

    // 加载设置
    loadSettings() {
      try {
        const domain = uni.getStorageSync('serverDomain');
        const key = uni.getStorageSync('accessKey');
        
        if (domain && key) {
          this.serverDomain = domain;
          this.accessKey = key;
          this.settingsConfigured = true;
        }
      } catch (error) {
        console.error('加载设置失败:', error);
      }
    },

    // 保存设置
    saveSettings() {
      if (!this.serverDomain || !this.accessKey) {
        uni.showToast({
          title: '请填写完整设置',
          icon: 'none'
        });
        return;
      }

      try {
        uni.setStorageSync('serverDomain', this.serverDomain);
        uni.setStorageSync('accessKey', this.accessKey);
        this.settingsConfigured = true;
        
        if (this.dbLoaded) {
          this.loadData();
        }
        
        uni.showToast({
          title: '设置保存成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('保存设置失败:', error);
        uni.showToast({
          title: '保存设置失败',
          icon: 'none'
        });
      }
    },

    // 加载数据
    async loadData() {
      if (!this.dbData) return;
      
      this.loading = true;
      try {
        switch (this.activeTab) {
          case 'images':
            await this.loadImages();
            break;
          case 'videos':
            await this.loadVideos();
            break;
          case 'files':
            await this.loadFiles();
            break;
        }
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 加载图片数据
    async loadImages() {
      // TODO: 从数据库读取图片数据并生成缩略图URL
      this.imageList = [];
    },

    // 加载视频数据
    async loadVideos() {
      // TODO: 从数据库读取视频数据
      this.videoList = [];
    },

    // 加载文件数据
    async loadFiles() {
      // TODO: 从数据库读取文件数据
      this.fileList = [];
    },

    // 生成文件URL
    generateFileUrl(filePath, type = 'file') {
      const today = new Date();
      const dateStr = today.getFullYear() + '-' + 
                     String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                     String(today.getDate()).padStart(2, '0');
      
      // 生成SHA256哈希 (需要实现)
      const hashStr = this.generateSHA256(this.accessKey + dateStr);
      
      // 构建URL
      const pathParts = filePath.split('/');
      const filename = pathParts[pathParts.length - 1];
      const relativePath = pathParts.slice(2, -1).join('/'); // 去掉 ../data/ 前缀
      
      return `${this.serverDomain}/${type}/${relativePath}/${hashStr}/${filename}`;
    },

    // 生成缩略图URL
    generateThumbnailUrl(filePath) {
      const url = this.generateFileUrl(filePath, 'file');
      const lastDotIndex = url.lastIndexOf('.');
      if (lastDotIndex > -1) {
        return url.substring(0, lastDotIndex) + '_tagimg' + url.substring(lastDotIndex);
      }
      return url + '_tagimg';
    },

    // 生成SHA256哈希 (需要实现)
    generateSHA256(text) {
      // TODO: 实现SHA256哈希算法
      return 'placeholder_hash';
    },

    // 获取当前列表
    getCurrentList() {
      switch (this.activeTab) {
        case 'images': return this.imageList;
        case 'videos': return this.videoList;
        case 'files': return this.fileList;
        default: return [];
      }
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleDateString();
    },

    // 图片预览
    previewImage(image, index) {
      // TODO: 实现图片预览功能
      console.log('预览图片:', image);
    },

    // 播放视频
    playVideo(video) {
      // TODO: 实现视频播放功能
      console.log('播放视频:', video);
    },

    // 下载文件
    downloadFile(file) {
      // TODO: 实现文件下载功能
      console.log('下载文件:', file);
    },

    // 图片加载错误
    onImageError(e) {
      console.error('图片加载失败:', e);
    },

    // 图片加载成功
    onImageLoad(e) {
      console.log('图片加载成功:', e);
    }
  }
}
</script>

<style scoped>
.container {
  flex: 1;
  background-color: #f8f8f8;
}

.tab-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15px 0;
  font-size: 16px;
  color: #666;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #007AFF;
  border-bottom-color: #007AFF;
}

.db-selector {
  padding: 40px 20px;
  text-align: center;
}

.select-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  margin-bottom: 10px;
}

.tip {
  color: #999;
  font-size: 14px;
}

.settings {
  padding: 20px;
  background-color: #fff;
  margin: 10px;
  border-radius: 8px;
}

.setting-item {
  margin-bottom: 15px;
}

.label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}

.input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.save-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  width: 100%;
}

.content {
  flex: 1;
  padding: 10px;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  width: calc(50% - 5px);
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.thumbnail {
  width: 100%;
  height: 150px;
}

.image-info {
  padding: 10px;
}

.filename {
  font-size: 12px;
  color: #333;
  display: block;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.date {
  font-size: 11px;
  color: #999;
}

.video-list, .file-list {
  background-color: #fff;
  border-radius: 8px;
}

.video-item, .file-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.video-item:last-child, .file-item:last-child {
  border-bottom: none;
}

.video-thumbnail {
  width: 60px;
  height: 60px;
  background-color: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.play-icon {
  font-size: 24px;
  color: #007AFF;
}

.file-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  margin-right: 15px;
}

.video-info, .file-info {
  flex: 1;
}

.video-info .filename, .file-info .filename {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.video-info .date, .file-info .date {
  font-size: 12px;
  color: #999;
  margin-bottom: 3px;
}

.video-info .size, .file-info .size {
  font-size: 12px;
  color: #666;
}

.loading, .empty {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}
</style>
