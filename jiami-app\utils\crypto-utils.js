/**
 * 加密解密工具类
 * 使用WebCrypto API进行AES-256-GCM解密
 */

/**
 * 生成SHA256哈希
 * @param {string} text 要哈希的文本
 * @returns {Promise<string>} 十六进制哈希字符串
 */
export async function generateSHA256(text) {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    return hashHex;
  } catch (error) {
    console.error('SHA256哈希生成失败:', error);
    throw error;
  }
}

/**
 * 将十六进制字符串转换为ArrayBuffer
 * @param {string} hex 十六进制字符串
 * @returns {ArrayBuffer}
 */
function hexToArrayBuffer(hex) {
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
  }
  return bytes.buffer;
}

/**
 * 将ArrayBuffer转换为十六进制字符串
 * @param {ArrayBuffer} buffer
 * @returns {string}
 */
function arrayBufferToHex(buffer) {
  const bytes = new Uint8Array(buffer);
  return Array.from(bytes).map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * AES-256-GCM解密
 * @param {string} encryptedDataHex 加密数据的十六进制字符串
 * @param {string} passwordHex 密码的十六进制字符串
 * @param {string} ivHex IV的十六进制字符串
 * @returns {Promise<ArrayBuffer>} 解密后的数据
 */
export async function decryptAES256GCM(encryptedDataHex, passwordHex, ivHex) {
  try {
    // 转换十六进制字符串为ArrayBuffer
    const encryptedData = hexToArrayBuffer(encryptedDataHex);
    const password = hexToArrayBuffer(passwordHex);
    const iv = hexToArrayBuffer(ivHex);

    // 导入密钥
    const key = await crypto.subtle.importKey(
      'raw',
      password,
      { name: 'AES-GCM' },
      false,
      ['decrypt']
    );

    // 解密数据
    const decryptedData = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      key,
      encryptedData
    );

    return decryptedData;
  } catch (error) {
    console.error('AES解密失败:', error);
    throw error;
  }
}

/**
 * 将解密后的ArrayBuffer转换为Blob URL用于图片显示
 * @param {ArrayBuffer} decryptedData 解密后的数据
 * @param {string} mimeType MIME类型，如 'image/jpeg'
 * @returns {string} Blob URL
 */
export function createBlobUrl(decryptedData, mimeType) {
  try {
    const blob = new Blob([decryptedData], { type: mimeType });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('创建Blob URL失败:', error);
    throw error;
  }
}

/**
 * 根据文件扩展名获取MIME类型
 * @param {string} filename 文件名
 * @returns {string} MIME类型
 */
export function getMimeType(filename) {
  const ext = filename.toLowerCase().split('.').pop();
  const mimeTypes = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'bmp': 'image/bmp',
    'heic': 'image/heic',
    'mp4': 'video/mp4',
    'avi': 'video/avi',
    'mov': 'video/quicktime',
    'wmv': 'video/x-ms-wmv',
    'flv': 'video/x-flv',
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'txt': 'text/plain',
    'zip': 'application/zip',
    'rar': 'application/x-rar-compressed'
  };
  return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * 生成今天的日期字符串 (YYYY-MM-DD格式)
 * @returns {string} 日期字符串
 */
export function getTodayDateString() {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * 生成文件访问URL
 * @param {string} filePath 文件路径 (如: ../data/file/2025-07-31/IMG20190621141002.jpg)
 * @param {string} serverDomain 服务器域名
 * @param {string} accessKey 访问密钥
 * @param {string} type 文件类型 ('file', 'video', 'other')
 * @returns {Promise<string>} 完整的文件访问URL
 */
export async function generateFileUrl(filePath, serverDomain, accessKey, type = 'file') {
  try {
    // 获取今天的日期
    const dateStr = getTodayDateString();
    
    // 生成SHA256哈希
    const hashStr = await generateSHA256(accessKey + dateStr);
    
    // 解析文件路径
    const pathParts = filePath.split('/');
    const filename = pathParts[pathParts.length - 1];
    const relativePath = pathParts.slice(2, -1).join('/'); // 去掉 ../data/ 前缀
    
    // 构建完整URL
    return `${serverDomain}/${type}/${relativePath}/${hashStr}/${filename}`;
  } catch (error) {
    console.error('生成文件URL失败:', error);
    throw error;
  }
}

/**
 * 生成缩略图URL
 * @param {string} filePath 原始文件路径
 * @param {string} serverDomain 服务器域名
 * @param {string} accessKey 访问密钥
 * @returns {Promise<string>} 缩略图URL
 */
export async function generateThumbnailUrl(filePath, serverDomain, accessKey) {
  try {
    const url = await generateFileUrl(filePath, serverDomain, accessKey, 'file');
    const lastDotIndex = url.lastIndexOf('.');
    if (lastDotIndex > -1) {
      return url.substring(0, lastDotIndex) + '_tagimg' + url.substring(lastDotIndex);
    }
    return url + '_tagimg';
  } catch (error) {
    console.error('生成缩略图URL失败:', error);
    throw error;
  }
}
