if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function requireNativePlugin(name) {
    return weex.requireModule(name);
  }
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$4 = {
    name: "Gallery",
    data() {
      return {
        // 基础数据
        allMediaItems: [],
        allImageItems: [],
        // 图片数据
        allVideoItems: [],
        // 视频数据
        totalImages: 0,
        totalVideos: 0,
        currentMediaType: "image",
        // 当前显示的媒体类型：'image' 或 'video'
        initialLoading: true,
        containerHeight: 0,
        imageCache: /* @__PURE__ */ new Map(),
        // 🚀 高性能下拉加载控制 - 优化批量大小
        loadBatchSize: 30,
        // 减少批量大小，提升响应速度
        loadOffset: 0,
        hasMoreData: true,
        loadingMore: false,
        loadMoreTimer: null,
        // 性能优化 - 增强节流和缓存
        backgroundLoadTimer: null,
        isScrolling: false,
        scrollTimer: null,
        renderTimer: null,
        visibleItems: /* @__PURE__ */ new Set(),
        // 可见项目缓存
        intersectionObserver: null
        // 交叉观察器
      };
    },
    computed: {
      // 当前显示的媒体项目
      allMediaItems() {
        return this.currentMediaType === "image" ? this.allImageItems : this.allVideoItems;
      },
      // 图片数量
      imageCount() {
        return this.allImageItems.length;
      },
      // 视频数量
      videoCount() {
        return this.allVideoItems.length;
      },
      // 当前媒体总数
      totalMediaCount() {
        return this.currentMediaType === "image" ? this.totalImages : this.totalVideos;
      },
      groupedMedia() {
        if (!this.allMediaItems || this.allMediaItems.length === 0) {
          return {};
        }
        const groups = {};
        this.allMediaItems.forEach((item) => {
          const dateKey = item.date || "未知日期";
          if (!groups[dateKey]) {
            groups[dateKey] = [];
          }
          groups[dateKey].push(item);
        });
        const sortedGroups = {};
        Object.keys(groups).sort((a, b) => {
          if (a === "未知日期")
            return 1;
          if (b === "未知日期")
            return -1;
          return new Date(b) - new Date(a);
        }).forEach((key) => {
          sortedGroups[key] = groups[key];
        });
        return sortedGroups;
      }
    },
    onLoad() {
      formatAppLog("log", "at pages/gallery/gallery.vue:188", "🚀 高性能相册页面加载");
      this.initializeGallery();
    },
    onUnload() {
      this.cleanup();
    },
    onHide() {
      this.pauseBackgroundTasks();
    },
    onShow() {
      this.resumeBackgroundTasks();
    },
    methods: {
      async initializeGallery() {
        formatAppLog("log", "at pages/gallery/gallery.vue:204", "开始初始化高性能相册...");
        try {
          this.initializeContainer();
          await this.loadAllMediaData();
          this.initialLoading = false;
        } catch (error) {
          formatAppLog("error", "at pages/gallery/gallery.vue:214", "❌ 相册初始化失败:", error);
          this.initialLoading = false;
          uni.showModal({
            title: "初始化失败",
            content: `相册初始化失败: ${error.message || "未知错误"}`,
            showCancel: false,
            confirmText: "重试",
            success: (res) => {
              if (res.confirm) {
                this.initialLoading = true;
                setTimeout(() => {
                  this.initializeGallery();
                }, 1e3);
              }
            }
          });
        }
      },
      initializeContainer() {
        const systemInfo = uni.getSystemInfoSync();
        this.containerHeight = systemInfo.windowHeight - 50;
        formatAppLog("log", "at pages/gallery/gallery.vue:238", "容器高度设置为:", this.containerHeight + "px");
      },
      // 🚀 支持分批加载的数据加载方法
      async loadAllMediaData() {
        formatAppLog("log", "at pages/gallery/gallery.vue:243", "开始加载媒体数据...");
        try {
          const hasPermission = await this.requestPermission();
          if (!hasPermission) {
            throw new Error("相册权限被拒绝");
          }
          const [firstImageBatch, firstVideoBatch] = await Promise.all([
            this.loadBatchImages(0, this.loadBatchSize),
            this.loadBatchVideos(0, this.loadBatchSize)
          ]);
          this.allImageItems = firstImageBatch || [];
          this.totalImages = this.allImageItems.length;
          this.allVideoItems = firstVideoBatch || [];
          this.totalVideos = this.allVideoItems.length;
          this.loadOffset = this.loadBatchSize;
          const currentBatch = this.currentMediaType === "image" ? firstImageBatch : firstVideoBatch;
          this.hasMoreData = currentBatch.length >= this.loadBatchSize;
          formatAppLog("log", "at pages/gallery/gallery.vue:271", `✅ 首批媒体数据加载完成: ${this.totalImages}张图片, ${this.totalVideos}个视频, 还有更多: ${this.hasMoreData}`);
        } catch (error) {
          formatAppLog("error", "at pages/gallery/gallery.vue:274", "❌ 加载媒体数据失败:", error);
          this.allImageItems = [];
          this.allVideoItems = [];
          this.totalImages = 0;
          this.totalVideos = 0;
          this.hasMoreData = false;
          throw error;
        }
      },
      // 🚀 高性能下拉加载功能
      // 加载更多图片（防抖优化）
      loadMoreImages() {
        if (this.loadMoreTimer) {
          clearTimeout(this.loadMoreTimer);
        }
        this.loadMoreTimer = setTimeout(() => {
          this.doLoadMoreImages();
        }, 200);
      },
      async doLoadMoreImages() {
        if (this.loadingMore || !this.hasMoreData) {
          formatAppLog("log", "at pages/gallery/gallery.vue:300", "⚠️ 正在加载或没有更多数据");
          return;
        }
        this.loadingMore = true;
        formatAppLog("log", "at pages/gallery/gallery.vue:305", `📱 开始加载更多图片, offset: ${this.loadOffset}`);
        try {
          const moreBatch = this.currentMediaType === "image" ? await this.loadBatchImages(this.loadOffset, this.loadBatchSize) : await this.loadBatchVideos(this.loadOffset, this.loadBatchSize);
          if (moreBatch && moreBatch.length > 0) {
            if (this.renderTimer) {
              clearTimeout(this.renderTimer);
            }
            this.renderTimer = setTimeout(() => {
              this.$nextTick(() => {
                if (this.currentMediaType === "image") {
                  this.allImageItems.push(...moreBatch);
                  this.totalImages = this.allImageItems.length;
                } else {
                  this.allVideoItems.push(...moreBatch);
                  this.totalVideos = this.allVideoItems.length;
                }
                this.loadOffset += moreBatch.length;
                this.hasMoreData = moreBatch.length >= this.loadBatchSize;
                const mediaType = this.currentMediaType === "image" ? "图片" : "视频";
                const totalCount = this.currentMediaType === "image" ? this.totalImages : this.totalVideos;
                formatAppLog("log", "at pages/gallery/gallery.vue:336", `✅ 加载更多完成: 新增${moreBatch.length}个${mediaType}, 总计${totalCount}个, 还有更多: ${this.hasMoreData}`);
              });
            }, 16);
          } else {
            this.hasMoreData = false;
            const mediaType = this.currentMediaType === "image" ? "图片" : "视频";
            formatAppLog("log", "at pages/gallery/gallery.vue:342", `⚠️ 没有更多${mediaType}了`);
          }
        } catch (error) {
          formatAppLog("error", "at pages/gallery/gallery.vue:347", "❌ 加载更多图片失败:", error);
          this.hasMoreData = false;
          uni.showToast({
            title: "加载失败，请重试",
            icon: "error"
          });
        } finally {
          this.loadingMore = false;
        }
      },
      async loadBatchImages(offset = 0, limit = 50) {
        formatAppLog("log", "at pages/gallery/gallery.vue:361", `App端: 开始分批查询MediaStore, offset: ${offset}, limit: ${limit}`);
        return new Promise((resolve) => {
          try {
            const main = plus.android.runtimeMainActivity();
            const ContentResolver = plus.android.importClass("android.content.ContentResolver");
            const MediaStore = plus.android.importClass("android.provider.MediaStore");
            const ContentUris = plus.android.importClass("android.content.ContentUris");
            const resolver = main.getContentResolver();
            const uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
            this.queryBatchMediaStore(resolver, uri, "image", offset, limit).then((images) => {
              formatAppLog("log", "at pages/gallery/gallery.vue:374", `✅ MediaStore分批查询成功: ${images.length}张图片`);
              resolve(images);
            }).catch((error) => {
              formatAppLog("error", "at pages/gallery/gallery.vue:377", "❌ MediaStore查询失败:", error);
              resolve([]);
            });
          } catch (error) {
            formatAppLog("error", "at pages/gallery/gallery.vue:382", "❌ 加载图片异常:", error);
            resolve([]);
          }
        });
      },
      // 🚀 分批加载视频文件
      async loadBatchVideos(offset = 0, limit = 50) {
        formatAppLog("log", "at pages/gallery/gallery.vue:406", `App端: 开始分批查询视频MediaStore, offset: ${offset}, limit: ${limit}`);
        return new Promise((resolve) => {
          try {
            const main = plus.android.runtimeMainActivity();
            const ContentResolver = plus.android.importClass("android.content.ContentResolver");
            const MediaStore = plus.android.importClass("android.provider.MediaStore");
            const ContentUris = plus.android.importClass("android.content.ContentUris");
            const resolver = main.getContentResolver();
            const uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
            this.queryBatchMediaStore(resolver, uri, "video", offset, limit).then((videos) => {
              formatAppLog("log", "at pages/gallery/gallery.vue:419", `✅ 视频MediaStore分批查询成功: ${videos.length}个视频`);
              resolve(videos);
            }).catch((error) => {
              formatAppLog("error", "at pages/gallery/gallery.vue:422", "❌ 视频MediaStore查询失败:", error);
              resolve([]);
            });
          } catch (error) {
            formatAppLog("error", "at pages/gallery/gallery.vue:427", "❌ 加载视频异常:", error);
            resolve([]);
          }
        });
      },
      // 查询分批媒体文件（支持分页）
      queryBatchMediaStore(resolver, uri, type, offset = 0, limit = 50) {
        return new Promise((resolve) => {
          let cursor = null;
          try {
            const MediaStore = plus.android.importClass("android.provider.MediaStore");
            const ContentUris = plus.android.importClass("android.content.ContentUris");
            const projection = [
              MediaStore.MediaColumns._ID,
              MediaStore.MediaColumns.DATE_MODIFIED,
              MediaStore.MediaColumns.DATA,
              // 文件路径
              MediaStore.MediaColumns.DISPLAY_NAME,
              // 文件名
              MediaStore.MediaColumns.SIZE
              // 文件大小
            ];
            const sortOrder = `${MediaStore.MediaColumns.DATE_MODIFIED} DESC`;
            formatAppLog("log", "at pages/gallery/gallery.vue:469", `📱 开始查询${type}文件, offset: ${offset}, limit: ${limit}`);
            const startTime = Date.now();
            cursor = resolver.query(uri, projection, null, null, sortOrder);
            const mediaFiles = [];
            let processedCount = 0;
            let skippedCount = 0;
            if (cursor && plus.android.invoke(cursor, "moveToFirst")) {
              const idColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns._ID);
              const dateColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DATE_MODIFIED);
              const dataColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DATA);
              const nameColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.DISPLAY_NAME);
              const sizeColumn = plus.android.invoke(cursor, "getColumnIndex", MediaStore.MediaColumns.SIZE);
              do {
                try {
                  if (skippedCount < offset) {
                    skippedCount++;
                    continue;
                  }
                  if (processedCount >= limit) {
                    formatAppLog("log", "at pages/gallery/gallery.vue:494", `✅ 达到批次限制: ${limit}`);
                    break;
                  }
                  const id = plus.android.invoke(cursor, "getLong", idColumn);
                  const dateModified = plus.android.invoke(cursor, "getLong", dateColumn);
                  const filePath = plus.android.invoke(cursor, "getString", dataColumn);
                  const fileName = plus.android.invoke(cursor, "getString", nameColumn) || `${type}_${id}`;
                  const fileSize = plus.android.invoke(cursor, "getLong", sizeColumn);
                  const contentUri = ContentUris.withAppendedId(uri, id);
                  const uriString = plus.android.invoke(contentUri, "toString");
                  const formatSize = (bytes) => {
                    if (bytes === 0)
                      return "0 B";
                    const k = 1024;
                    const sizes = ["B", "KB", "MB", "GB"];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
                  };
                  const mediaItem = {
                    id: `${type}_${id}`,
                    url: type === "image" ? uriString : null,
                    // 图片使用URI，视频不需要URL
                    originalUrl: uriString,
                    // 保存原始URL用于播放
                    filePath,
                    name: fileName,
                    size: formatSize(fileSize),
                    date: new Date(dateModified * 1e3).toLocaleDateString(),
                    dateTime: new Date(dateModified * 1e3),
                    // 保存完整日期时间用于排序
                    type,
                    loading: false,
                    cached: false
                  };
                  mediaFiles.push(mediaItem);
                  processedCount++;
                } catch (itemError) {
                  formatAppLog("error", "at pages/gallery/gallery.vue:535", "❌ 处理媒体项失败:", itemError);
                }
              } while (plus.android.invoke(cursor, "moveToNext"));
              formatAppLog("log", "at pages/gallery/gallery.vue:539", `📋 ${type}文件遍历完成，共处理 ${processedCount} 个文件`);
            } else {
              formatAppLog("log", "at pages/gallery/gallery.vue:541", `⚠️ 没有找到${type}文件或cursor为空`);
            }
            const loadTime = Date.now() - startTime;
            formatAppLog("log", "at pages/gallery/gallery.vue:545", `✅ ${type}查询完成: ${mediaFiles.length}个文件, 耗时: ${loadTime}ms`);
            resolve(mediaFiles);
          } catch (error) {
            formatAppLog("error", "at pages/gallery/gallery.vue:549", `❌ 查询${type}失败:`, error);
            resolve([]);
          } finally {
            if (cursor) {
              try {
                plus.android.invoke(cursor, "close");
                formatAppLog("log", "at pages/gallery/gallery.vue:556", "📝 Cursor已关闭");
              } catch (closeError) {
                formatAppLog("error", "at pages/gallery/gallery.vue:558", "❌ 关闭cursor失败:", closeError);
              }
            }
          }
        });
      },
      // 生成所有模拟图片数据（用于分批加载）
      generateAllMockImages(totalCount = 500) {
        const mockImages = [];
        for (let i = 1; i <= totalCount; i++) {
          const daysAgo = Math.floor(i / 15);
          const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1e3);
          mockImages.push({
            id: `mock_image_${i}`,
            url: `https://picsum.photos/300/300?random=${i}`,
            name: `模拟图片${i}.jpg`,
            size: `${(Math.random() * 3 + 0.5).toFixed(1)}MB`,
            date: date.toLocaleDateString(),
            type: "image",
            loading: false,
            cached: false
          });
        }
        return mockImages;
      },
      // 生成模拟图片数据（用于调试）
      generateMockImages(count = 30) {
        const mockImages = [];
        for (let i = 1; i <= count; i++) {
          const daysAgo = Math.floor(i / 10);
          const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1e3);
          mockImages.push({
            id: `mock_image_${i}`,
            url: `https://picsum.photos/300/300?random=${i}`,
            name: `模拟图片${i}.jpg`,
            size: `${(Math.random() * 3 + 0.5).toFixed(1)}MB`,
            date: date.toLocaleDateString(),
            type: "image",
            loading: false,
            cached: false
          });
        }
        return mockImages;
      },
      // 生成所有模拟视频数据（用于分批加载）
      generateAllMockVideos(totalCount = 200) {
        const mockVideos = [];
        for (let i = 1; i <= totalCount; i++) {
          const daysAgo = Math.floor(i / 10);
          const date = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1e3);
          const duration = Math.floor(Math.random() * 300 + 30);
          const minutes = Math.floor(duration / 60);
          const seconds = duration % 60;
          const durationStr = `${minutes}:${seconds.toString().padStart(2, "0")}`;
          mockVideos.push({
            id: `mock_video_${i}`,
            url: null,
            // 视频不需要URL
            originalUrl: `https://sample-videos.com/zip/10/mp4/SampleVideo_${i}.mp4`,
            filePath: `/storage/emulated/0/Movies/模拟视频${i}.mp4`,
            name: `模拟视频${i}.mp4`,
            size: `${(Math.random() * 50 + 10).toFixed(1)}MB`,
            date: date.toLocaleDateString(),
            dateTime: date,
            type: "video",
            duration: durationStr,
            loading: false,
            cached: false
          });
        }
        return mockVideos;
      },
      // 🚀 切换媒体类型
      switchMediaType(type) {
        if (this.currentMediaType === type)
          return;
        formatAppLog("log", "at pages/gallery/gallery.vue:648", `切换媒体类型: ${this.currentMediaType} -> ${type}`);
        this.currentMediaType = type;
        this.loadOffset = this.loadBatchSize;
        const currentItems = type === "image" ? this.allImageItems : this.allVideoItems;
        this.hasMoreData = currentItems.length >= this.loadBatchSize;
        formatAppLog("log", "at pages/gallery/gallery.vue:658", `✅ 切换完成，当前显示${type === "image" ? "图片" : "视频"}: ${currentItems.length}个`);
      },
      async requestPermission() {
        return new Promise((resolve) => {
          plus.android.requestPermissions(
            ["android.permission.READ_EXTERNAL_STORAGE"],
            (result) => {
              resolve(result.granted.length > 0);
            },
            (error) => {
              formatAppLog("error", "at pages/gallery/gallery.vue:670", "权限请求失败:", error);
              resolve(false);
            }
          );
        });
      },
      async skipToMockData() {
        try {
          this.initializeContainer();
          this.allImageItems = this.generateMockImages(30);
          this.allVideoItems = this.generateAllMockVideos(20).slice(0, 20);
          this.totalImages = this.allImageItems.length;
          this.totalVideos = this.allVideoItems.length;
          this.loadOffset = Math.max(this.allImageItems.length, this.allVideoItems.length);
          this.hasMoreData = false;
          this.initialLoading = false;
        } catch (error) {
          formatAppLog("error", "at pages/gallery/gallery.vue:695", "❌ 模拟数据模式失败:", error);
          this.initialLoading = false;
        }
      },
      async loadQuickMode() {
        try {
          this.initializeContainer();
          const hasPermission = await this.requestPermission();
          if (!hasPermission) {
            this.skipToMockData();
            return;
          }
          const [quickImages, quickVideos] = await Promise.all([
            this.loadBatchImages(0, 20),
            this.loadBatchVideos(0, 10)
          ]);
          this.allImageItems = quickImages;
          this.allVideoItems = quickVideos;
          this.totalImages = this.allImageItems.length;
          this.totalVideos = this.allVideoItems.length;
          this.loadOffset = 20;
          this.hasMoreData = quickImages.length >= 20 || quickVideos.length >= 10;
          this.initialLoading = false;
        } catch (error) {
          formatAppLog("error", "at pages/gallery/gallery.vue:727", "❌ 快速加载模式失败:", error);
          this.skipToMockData();
        }
      },
      handleImageLoad(item) {
        item.loading = false;
      },
      handleImageError(item) {
        item.loading = false;
        if (item.type === "video" && item.originalUrl && item.url !== item.originalUrl) {
          formatAppLog("warn", "at pages/gallery/gallery.vue:741", `⚠️ 视频缩略图加载失败，尝试使用原始视频URI: ${item.id}`);
        }
      },
      handleItemClick(item) {
        if (item.type === "image") {
          const imageUrls = this.allMediaItems.filter((img) => img.type === "image").map((img) => img.url);
          uni.previewImage({
            urls: imageUrls,
            current: item.url,
            fail: (error) => {
              uni.showToast({
                title: "图片预览失败",
                icon: "error"
              });
            }
          });
        } else if (item.type === "video") {
          const videoUrl = item.originalUrl || item.url;
          uni.previewImage({
            urls: [videoUrl],
            current: videoUrl,
            fail: (error) => {
              formatAppLog("warn", "at pages/gallery/gallery.vue:772", "⚠️ 视频预览失败，尝试其他方式:", error);
              uni.showToast({
                title: "视频预览暂不支持",
                icon: "none",
                duration: 2e3
              });
            }
          });
        }
      },
      // 🚀 性能优化 - 滚动处理
      handleScroll(e) {
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer);
        }
        this.isScrolling = true;
        this.scrollTimer = setTimeout(() => {
          this.isScrolling = false;
          this.optimizeVisibleItems(e.detail.scrollTop);
        }, 100);
      },
      // 优化可见项目渲染
      optimizeVisibleItems(scrollTop) {
        const viewportHeight = this.containerHeight;
        const itemHeight = 100;
        const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 10);
        const endIndex = Math.min(this.allMediaItems.length, Math.ceil((scrollTop + viewportHeight) / itemHeight) + 10);
        this.visibleItems.clear();
        for (let i = startIndex; i < endIndex; i++) {
          if (this.allMediaItems[i]) {
            this.visibleItems.add(this.allMediaItems[i].id);
          }
        }
      },
      // 暂停后台任务
      pauseBackgroundTasks() {
        if (this.backgroundLoadTimer) {
          clearTimeout(this.backgroundLoadTimer);
          this.backgroundLoadTimer = null;
        }
      },
      // 恢复后台任务
      resumeBackgroundTasks() {
        this.scheduleBackgroundOptimization();
      },
      // 调度后台优化
      scheduleBackgroundOptimization() {
        if (this.backgroundLoadTimer)
          return;
        this.backgroundLoadTimer = setTimeout(() => {
          this.performBackgroundOptimization();
          this.backgroundLoadTimer = null;
        }, 2e3);
      },
      // 执行后台优化
      performBackgroundOptimization() {
        if (this.imageCache.size > 100) {
          const keysToDelete = [];
          let count = 0;
          for (const [key] of this.imageCache) {
            if (count++ > 50) {
              keysToDelete.push(key);
            }
          }
          keysToDelete.forEach((key) => this.imageCache.delete(key));
        }
      },
      cleanup() {
        if (this.loadMoreTimer) {
          clearTimeout(this.loadMoreTimer);
          this.loadMoreTimer = null;
        }
        if (this.scrollTimer) {
          clearTimeout(this.scrollTimer);
          this.scrollTimer = null;
        }
        if (this.backgroundLoadTimer) {
          clearTimeout(this.backgroundLoadTimer);
          this.backgroundLoadTimer = null;
        }
        if (this.renderTimer) {
          clearTimeout(this.renderTimer);
          this.renderTimer = null;
        }
        if (this.imageCache) {
          this.imageCache.clear();
        }
        if (this.visibleItems) {
          this.visibleItems.clear();
        }
        formatAppLog("log", "at pages/gallery/gallery.vue:886", "🚀 高性能相册组件已清理");
      }
    }
  };
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createCommentVNode(" 加载状态 "),
      $data.initialLoading ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "loading"
      }, [
        vue.createElementVNode("view", { class: "spinner" }),
        vue.createElementVNode("text", null, "正在扫描本机相册..."),
        vue.createElementVNode("text", { class: "loading-tip" }, "首次加载可能需要较长时间"),
        vue.createCommentVNode(" 调试按钮 "),
        vue.createElementVNode("view", { class: "debug-actions" }, [
          vue.createElementVNode("button", {
            onClick: _cache[0] || (_cache[0] = (...args) => $options.skipToMockData && $options.skipToMockData(...args)),
            class: "debug-btn"
          }, "跳过使用模拟数据"),
          vue.createElementVNode("button", {
            onClick: _cache[1] || (_cache[1] = (...args) => $options.loadQuickMode && $options.loadQuickMode(...args)),
            class: "debug-btn quick-btn"
          }, "快速加载模式")
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 媒体类型切换按钮 "),
      !$data.initialLoading ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "media-tabs"
      }, [
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["media-tab", { active: $data.currentMediaType === "image" }]),
            onClick: _cache[2] || (_cache[2] = ($event) => $options.switchMediaType("image"))
          },
          " 📷 图片 ",
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["media-tab", { active: $data.currentMediaType === "video" }]),
            onClick: _cache[3] || (_cache[3] = ($event) => $options.switchMediaType("video"))
          },
          " 🎬 视频 ",
          2
          /* CLASS */
        )
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 🚀 高性能相册列表 - 支持下拉加载，优化滚动性能 "),
      !$data.initialLoading ? (vue.openBlock(), vue.createElementBlock(
        "scroll-view",
        {
          key: 2,
          class: "gallery-scroll",
          "scroll-y": "true",
          onScroll: _cache[4] || (_cache[4] = (...args) => $options.handleScroll && $options.handleScroll(...args)),
          onScrolltolower: _cache[5] || (_cache[5] = (...args) => $options.loadMoreImages && $options.loadMoreImages(...args)),
          style: vue.normalizeStyle({ height: $data.containerHeight + "px" }),
          "lower-threshold": 150,
          throttle: false,
          "enable-back-to-top": true,
          "scroll-with-animation": false
        },
        [
          vue.createCommentVNode(" 图片网格布局 "),
          $data.currentMediaType === "image" ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "media-grid"
          }, [
            (vue.openBlock(true), vue.createElementBlock(
              vue.Fragment,
              null,
              vue.renderList($options.allMediaItems, (item) => {
                return vue.openBlock(), vue.createElementBlock("view", {
                  key: item.id,
                  class: "media-item",
                  onClick: ($event) => $options.handleItemClick(item)
                }, [
                  vue.createElementVNode("image", {
                    src: item.url,
                    class: "media-image",
                    mode: "aspectFill",
                    "lazy-load": true,
                    webp: true,
                    "fade-show": false,
                    "show-menu-by-longpress": false,
                    onLoad: ($event) => $options.handleImageLoad(item),
                    onError: ($event) => $options.handleImageError(item)
                  }, null, 40, ["src", "onLoad", "onError"]),
                  vue.createCommentVNode(" 加载状态 "),
                  item.loading ? (vue.openBlock(), vue.createElementBlock("view", {
                    key: 0,
                    class: "loading-overlay"
                  }, [
                    vue.createElementVNode("view", { class: "mini-spinner" })
                  ])) : vue.createCommentVNode("v-if", true)
                ], 8, ["onClick"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ])) : (vue.openBlock(), vue.createElementBlock(
            vue.Fragment,
            { key: 1 },
            [
              vue.createCommentVNode(" 视频列表布局 "),
              vue.createElementVNode("view", { class: "video-list" }, [
                (vue.openBlock(true), vue.createElementBlock(
                  vue.Fragment,
                  null,
                  vue.renderList($options.allMediaItems, (item) => {
                    return vue.openBlock(), vue.createElementBlock("view", {
                      key: item.id,
                      class: "video-item",
                      onClick: ($event) => $options.handleItemClick(item)
                    }, [
                      vue.createElementVNode("view", { class: "video-icon" }, "🎬"),
                      vue.createElementVNode("view", { class: "video-info" }, [
                        vue.createElementVNode(
                          "view",
                          { class: "video-name" },
                          vue.toDisplayString(item.name),
                          1
                          /* TEXT */
                        ),
                        vue.createElementVNode("view", { class: "video-details" }, [
                          vue.createElementVNode(
                            "text",
                            { class: "video-size" },
                            vue.toDisplayString(item.size),
                            1
                            /* TEXT */
                          ),
                          vue.createElementVNode(
                            "text",
                            { class: "video-date" },
                            vue.toDisplayString(item.date),
                            1
                            /* TEXT */
                          )
                        ])
                      ]),
                      vue.createElementVNode("view", { class: "video-arrow" }, "›")
                    ], 8, ["onClick"]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ])
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )),
          vue.createCommentVNode(" 🚀 高性能下拉加载更多状态 "),
          $data.hasMoreData && $options.allMediaItems.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 2,
            class: "load-more"
          }, [
            $data.loadingMore ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "loading-more"
            }, [
              vue.createElementVNode("view", { class: "mini-spinner" }),
              vue.createElementVNode("text", { class: "loading-text" }, "加载更多图片...")
            ])) : (vue.openBlock(), vue.createElementBlock("text", {
              key: 1,
              class: "load-more-text"
            }, "下拉加载更多"))
          ])) : vue.createCommentVNode("v-if", true),
          vue.createCommentVNode(" 没有更多数据 "),
          !$data.hasMoreData && $options.allMediaItems.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 3,
            class: "no-more"
          }, [
            vue.createElementVNode("text", { class: "no-more-text" }, "已加载全部图片")
          ])) : vue.createCommentVNode("v-if", true),
          vue.createCommentVNode(" 空状态 "),
          $options.allMediaItems.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 4,
            class: "empty-state"
          }, [
            vue.createElementVNode("text", { class: "empty-icon" }, "🖼️"),
            vue.createElementVNode("text", { class: "empty-text" }, "暂无图片文件")
          ])) : vue.createCommentVNode("v-if", true)
        ],
        36
        /* STYLE, NEED_HYDRATION */
      )) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesGalleryGallery = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$4], ["__scopeId", "data-v-ff88f784"], ["__file", "D:/phpStudy/WWW/jiami-app/pages/gallery/gallery.vue"]]);
  const _sfc_main$3 = {
    name: "Index",
    data() {
      return {
        webviewSrc: "/static/html/gallery.html"
      };
    },
    onLoad() {
      formatAppLog("log", "at pages/index/index.vue:23", "Index页面加载");
      this.initGallery();
    },
    onShow() {
      formatAppLog("log", "at pages/index/index.vue:27", "Index页面显示");
    },
    methods: {
      // 初始化相册数据
      initGallery() {
        const mockData = {
          images: [
            {
              id: 1,
              url: "/static/images/sample1.jpg",
              name: "示例图片1.jpg",
              size: "2.5MB",
              date: "2024-01-15"
            },
            {
              id: 2,
              url: "/static/images/sample2.jpg",
              name: "示例图片2.jpg",
              size: "1.8MB",
              date: "2024-01-14"
            }
          ],
          videos: [
            {
              id: 1,
              url: "/static/videos/sample1.mp4",
              thumbnail: "/static/images/video-thumb1.jpg",
              name: "示例视频1.mp4",
              size: "15.2MB",
              duration: "00:02:30",
              date: "2024-01-13"
            }
          ]
        };
        setTimeout(() => {
          this.sendDataToWebview(mockData);
        }, 1e3);
      },
      // 向webview发送数据
      sendDataToWebview(data) {
        const currentWebview = this.$scope.$getAppWebview();
        if (currentWebview) {
          setTimeout(() => {
            const webview = currentWebview.children()[0];
            if (webview) {
              webview.evalJS(`
							if (window.updateGalleryData) {
								window.updateGalleryData(${JSON.stringify(data)});
							}
						`);
            }
          }, 500);
        }
      },
      // 处理webview发送的消息
      handleMessage(event) {
        formatAppLog("log", "at pages/index/index.vue:92", "收到webview消息:", event.detail.data);
        const data = event.detail.data[0];
        if (data.type === "ready") {
          formatAppLog("log", "at pages/index/index.vue:97", "WebView已准备就绪");
        } else if (data.type === "imageClick") {
          this.handleImageClick(data.payload);
        } else if (data.type === "videoClick") {
          this.handleVideoClick(data.payload);
        }
      },
      // 处理图片点击
      handleImageClick(imageData) {
        formatAppLog("log", "at pages/index/index.vue:109", "图片被点击:", imageData);
        uni.showToast({
          title: `点击了图片: ${imageData.name}`,
          icon: "none"
        });
      },
      // 处理视频点击
      handleVideoClick(videoData) {
        formatAppLog("log", "at pages/index/index.vue:119", "视频被点击:", videoData);
        uni.showToast({
          title: `点击了视频: ${videoData.name}`,
          icon: "none"
        });
      },
      // webview加载完成
      handleLoad(event) {
        formatAppLog("log", "at pages/index/index.vue:129", "WebView加载完成");
      },
      // webview加载错误
      handleError(event) {
        formatAppLog("error", "at pages/index/index.vue:134", "WebView加载错误:", event);
        uni.showToast({
          title: "WebView加载失败",
          icon: "error"
        });
      }
    }
  };
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createCommentVNode(" WebView 组件加载本地HTML页面 "),
      vue.createElementVNode("web-view", {
        class: "webview",
        src: $data.webviewSrc,
        onMessage: _cache[0] || (_cache[0] = (...args) => $options.handleMessage && $options.handleMessage(...args)),
        onLoad: _cache[1] || (_cache[1] = (...args) => $options.handleLoad && $options.handleLoad(...args)),
        onError: _cache[2] || (_cache[2] = (...args) => $options.handleError && $options.handleError(...args))
      }, null, 40, ["src"])
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$3], ["__scopeId", "data-v-1cf27b2a"], ["__file", "D:/phpStudy/WWW/jiami-app/pages/index/index.vue"]]);
  class WebDAVClient {
    constructor(baseUrl, options = {}) {
      this.baseUrl = baseUrl.replace(/\/$/, "");
      this.user = options.user || "";
      this.password = options.password || "";
      this.timeout = options.timeout || 3e4;
      this.debug = options.debug || false;
      this.authHeader = this.user && this.password ? "Basic " + btoa(this.user + ":" + this.password) : null;
    }
    // 发送HTTP请求
    async _request(method, url, body = null, headers = {}) {
      const requestHeaders = {
        ...headers
      };
      if (this.authHeader) {
        requestHeaders["Authorization"] = this.authHeader;
      }
      if (this.debug) {
        formatAppLog("log", "at pages/webdav/webdav.vue:202", `WebDAV Request: ${method} ${url}`, { headers: requestHeaders, body });
      }
      return new Promise((resolve, reject) => {
        const requestConfig = {
          url,
          method,
          header: requestHeaders,
          timeout: this.timeout,
          success: (res) => {
            if (this.debug) {
              formatAppLog("log", "at pages/webdav/webdav.vue:213", `WebDAV Response: ${res.statusCode}`, res);
            }
            const response = {
              status: res.statusCode,
              statusText: res.statusCode === 200 ? "OK" : "Error",
              ok: res.statusCode >= 200 && res.statusCode < 300,
              headers: {
                get: (name) => {
                  const lowerName = name.toLowerCase();
                  for (const key in res.header) {
                    if (key.toLowerCase() === lowerName) {
                      return res.header[key];
                    }
                  }
                  return null;
                }
              },
              text: () => Promise.resolve(typeof res.data === "string" ? res.data : JSON.stringify(res.data)),
              arrayBuffer: () => {
                if (res.data instanceof ArrayBuffer) {
                  return Promise.resolve(res.data);
                } else if (typeof res.data === "string") {
                  const encoder = new TextEncoder();
                  return Promise.resolve(encoder.encode(res.data).buffer);
                } else {
                  return Promise.resolve(new ArrayBuffer(0));
                }
              }
            };
            resolve(response);
          },
          fail: (error) => {
            if (this.debug) {
              formatAppLog("error", "at pages/webdav/webdav.vue:249", "WebDAV Request failed:", error);
            }
            reject(new Error(`Request failed: ${error.errMsg || error.message}`));
          }
        };
        if (body) {
          requestConfig.data = body;
        }
        uni.request(requestConfig);
      });
    }
    // 测试连接 - 简化版本
    async ping() {
      try {
        const response = await this._request("PROPFIND", this.baseUrl + "/", `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
          "Content-Type": "application/xml; charset=utf-8",
          "Depth": "0"
        });
        return response.status === 207 || response.status === 200;
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:280", "Ping failed:", error);
        return false;
      }
    }
    // 读取目录内容
    async readDir(path = "/") {
      const normalizedPath = this._normalizePath(path);
      const propfindXml = `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:displayname/>
    <D:getcontentlength/>
    <D:getcontenttype/>
    <D:getlastmodified/>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`;
      try {
        const response = await this._request("PROPFIND", this.baseUrl + normalizedPath, propfindXml, {
          "Content-Type": "application/xml; charset=utf-8",
          "Depth": "1"
        });
        if (response.status === 207 || response.status === 200) {
          const xmlText = await response.text();
          return this._parseMultiStatus(xmlText, normalizedPath);
        } else {
          if (this.debug) {
            formatAppLog("log", "at pages/webdav/webdav.vue:311", `ReadDir failed with status ${response.status}`);
            const errorText = await response.text();
            formatAppLog("log", "at pages/webdav/webdav.vue:313", "Response:", errorText);
          }
          throw new Error(`Failed to read directory: ${response.status}`);
        }
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:318", "ReadDir failed:", error);
        throw error;
      }
    }
    // 规范化路径
    _normalizePath(path) {
      if (!path.startsWith("/")) {
        path = "/" + path;
      }
      path = path.replace(/\/+/g, "/");
      const pathParts = path.split("/");
      const encodedParts = pathParts.map((part) => {
        if (part === "")
          return part;
        return encodeURIComponent(part);
      });
      return encodedParts.join("/");
    }
    // 解析多状态XML响应
    _parseMultiStatus(xmlText, basePath) {
      const files = [];
      try {
        const responseRegex = /<D:response[^>]*>([\s\S]*?)<\/D:response>/gi;
        let match;
        while ((match = responseRegex.exec(xmlText)) !== null) {
          const responseXml = match[1];
          const file = this._parseFileFromResponse(responseXml, basePath);
          if (file && file.path !== basePath) {
            files.push(file);
          }
        }
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:355", "XML parsing failed:", error);
      }
      return files;
    }
    // 从响应中解析文件信息
    _parseFileFromResponse(responseXml, basePath) {
      try {
        const hrefMatch = responseXml.match(/<D:href[^>]*>([^<]+)<\/D:href>/i);
        if (!hrefMatch)
          return null;
        let path = decodeURIComponent(hrefMatch[1]);
        if (path.startsWith(this.baseUrl)) {
          path = path.substring(this.baseUrl.length);
        }
        const name = path.split("/").filter((p) => p).pop() || "";
        const isDirectory = responseXml.includes("<D:collection") || responseXml.includes("<D:resourcetype><D:collection/></D:resourcetype>");
        const sizeMatch = responseXml.match(/<D:getcontentlength[^>]*>([^<]+)<\/D:getcontentlength>/i);
        const size = sizeMatch ? parseInt(sizeMatch[1]) : 0;
        const typeMatch = responseXml.match(/<D:getcontenttype[^>]*>([^<]+)<\/D:getcontenttype>/i);
        const contentType = typeMatch ? typeMatch[1] : "";
        const modifiedMatch = responseXml.match(/<D:getlastmodified[^>]*>([^<]+)<\/D:getlastmodified>/i);
        const lastModified = modifiedMatch ? new Date(modifiedMatch[1]) : /* @__PURE__ */ new Date();
        return {
          name,
          path,
          size,
          isDirectory,
          lastModified,
          contentType
        };
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:393", "File parsing failed:", error);
        return null;
      }
    }
    // 创建目录
    async mkdir(path) {
      const normalizedPath = this._normalizePath(path);
      try {
        const response = await this._request("MKCOL", this.baseUrl + normalizedPath);
        if (response.status !== 201 && response.status !== 405) {
          throw new Error(`Failed to create directory: ${response.status}`);
        }
        return true;
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:408", "Mkdir failed:", error);
        throw error;
      }
    }
    // 删除文件或目录
    async remove(path) {
      const normalizedPath = this._normalizePath(path);
      try {
        const response = await this._request("DELETE", this.baseUrl + normalizedPath);
        if (response.status !== 200 && response.status !== 204 && response.status !== 404) {
          throw new Error(`Failed to remove: ${response.status}`);
        }
        return true;
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:423", "Remove failed:", error);
        throw error;
      }
    }
    // 重命名/移动文件或目录
    async rename(oldPath, newPath, overwrite = true) {
      const normalizedOldPath = this._normalizePath(oldPath);
      const normalizedNewPath = this._normalizePath(newPath);
      const destination = this.baseUrl + normalizedNewPath;
      try {
        const response = await this._request("MOVE", this.baseUrl + normalizedOldPath, null, {
          "Destination": destination,
          "Overwrite": overwrite ? "T" : "F"
        });
        if (response.status !== 201 && response.status !== 204) {
          throw new Error(`Failed to rename: ${response.status}`);
        }
        return true;
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:445", "Rename failed:", error);
        throw error;
      }
    }
    // 下载文件
    async downloadFile(path, onProgress = null) {
      const normalizedPath = this._normalizePath(path);
      try {
        const response = await this._request("GET", this.baseUrl + normalizedPath);
        if (response.status !== 200) {
          throw new Error(`Failed to download file: ${response.status}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        if (onProgress) {
          onProgress(arrayBuffer.byteLength, arrayBuffer.byteLength);
        }
        return new Uint8Array(arrayBuffer);
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:467", "Download failed:", error);
        throw error;
      }
    }
    // 上传文件
    async uploadFile(path, fileData, onProgress = null) {
      const normalizedPath = this._normalizePath(path);
      try {
        const response = await this._request("PUT", this.baseUrl + normalizedPath, fileData, {
          "Content-Type": "application/octet-stream"
        });
        if (response.status !== 200 && response.status !== 201 && response.status !== 204) {
          throw new Error(`Failed to upload file: ${response.status}`);
        }
        if (onProgress) {
          const size = fileData.byteLength || fileData.length || 0;
          onProgress(size, size);
        }
        return true;
      } catch (error) {
        if (this.debug)
          formatAppLog("error", "at pages/webdav/webdav.vue:491", "Upload failed:", error);
        throw error;
      }
    }
    // 测试路径是否存在
    async pathExists(path) {
      try {
        const normalizedPath = this._normalizePath(path);
        const response = await this._request("PROPFIND", this.baseUrl + normalizedPath, `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
          "Content-Type": "application/xml; charset=utf-8",
          "Depth": "0"
        });
        return response.status === 207 || response.status === 200;
      } catch (error) {
        return false;
      }
    }
  }
  const _sfc_main$2 = {
    name: "WebDAV",
    data() {
      return {
        // 连接配置
        config: {
          serverUrl: "",
          username: "",
          password: "",
          rootPath: "/"
        },
        // 连接状态
        isConnected: false,
        connecting: false,
        webdavClient: null,
        // 文件浏览
        currentPath: "/",
        fileList: [],
        loading: false,
        // 进度显示
        showProgress: false,
        progressTitle: "",
        progressText: "",
        progressPercent: 0,
        canCancelProgress: false,
        // 其他状态
        selectedFile: null
      };
    },
    computed: {
      // 检查配置是否有效
      isConfigValid() {
        return this.config.serverUrl.trim() && this.config.username.trim() && this.config.password.trim();
      },
      // 路径分段
      pathSegments() {
        const segments = [{ name: "根目录", path: "/" }];
        if (this.currentPath !== "/") {
          const parts = this.currentPath.split("/").filter((p) => p);
          let currentPath = "";
          parts.forEach((part) => {
            currentPath += "/" + part;
            segments.push({ name: part, path: currentPath });
          });
        }
        return segments;
      }
    },
    onLoad() {
      formatAppLog("log", "at pages/webdav/webdav.vue:575", "WebDAV页面加载");
      this.loadSavedConfig();
      this.checkNetworkStatus();
    },
    onShow() {
      this.checkNetworkStatus();
    },
    methods: {
      // 加载保存的配置
      loadSavedConfig() {
        try {
          const savedConfig = uni.getStorageSync("webdav_config");
          if (savedConfig) {
            this.config = { ...this.config, ...savedConfig };
          }
        } catch (error) {
          formatAppLog("error", "at pages/webdav/webdav.vue:594", "加载配置失败:", error);
        }
      },
      // 保存配置
      saveConfig() {
        try {
          uni.setStorageSync("webdav_config", this.config);
        } catch (error) {
          formatAppLog("error", "at pages/webdav/webdav.vue:603", "保存配置失败:", error);
        }
      },
      // 连接到WebDAV服务器
      async connectToWebDAV() {
        if (!this.isConfigValid) {
          uni.showToast({
            title: "请填写完整的连接信息",
            icon: "none"
          });
          return;
        }
        const urlPattern = /^https?:\/\/.+/i;
        if (!urlPattern.test(this.config.serverUrl.trim())) {
          uni.showModal({
            title: "服务器地址格式错误",
            content: "请输入正确的服务器地址格式：\n\n示例：\n• https://cloud.example.com/remote.php/dav/files/username/\n• http://*************:8080/webdav/\n• https://your-domain.com/webdav\n\n注意：路径中的中文字符会自动进行URL编码",
            showCancel: false,
            confirmText: "我知道了"
          });
          return;
        }
        this.connecting = true;
        try {
          this.webdavClient = new WebDAVClient(this.config.serverUrl, {
            user: this.config.username,
            password: this.config.password,
            debug: true
          });
          const testAuth = btoa(`${this.config.username}:${this.config.password}`);
          formatAppLog("log", "at pages/webdav/webdav.vue:641", "Expected Basic auth:", `Basic ${testAuth}`);
          formatAppLog("log", "at pages/webdav/webdav.vue:642", "WebDAV Base URL:", this.config.serverUrl);
          formatAppLog("log", "at pages/webdav/webdav.vue:643", "Root Path:", this.config.rootPath || "/");
          let connectionSuccessful = false;
          let actualRootPath = "/";
          try {
            formatAppLog("log", "at pages/webdav/webdav.vue:650", "Testing WebDAV server root path...");
            const rootTestResponse = await this.webdavClient._request("PROPFIND", this.webdavClient.baseUrl + "/", `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
              "Content-Type": "application/xml; charset=utf-8",
              "Depth": "0"
            });
            if (rootTestResponse.status === 207 || rootTestResponse.status === 200) {
              formatAppLog("log", "at pages/webdav/webdav.vue:664", "WebDAV server root accessible");
              const userPath = this.config.rootPath || "/";
              if (userPath !== "/") {
                formatAppLog("log", "at pages/webdav/webdav.vue:669", "Testing user specified path:", userPath);
                try {
                  const userPathResponse = await this.webdavClient._request("PROPFIND", this.webdavClient.baseUrl + this.webdavClient._normalizePath(userPath), `<?xml version="1.0" encoding="UTF-8"?>
<D:propfind xmlns:D="DAV:">
  <D:prop>
    <D:resourcetype/>
  </D:prop>
</D:propfind>`, {
                    "Content-Type": "application/xml; charset=utf-8",
                    "Depth": "0"
                  });
                  if (userPathResponse.status === 207 || userPathResponse.status === 200) {
                    actualRootPath = userPath;
                    formatAppLog("log", "at pages/webdav/webdav.vue:683", "User specified path accessible");
                  } else {
                    formatAppLog("log", "at pages/webdav/webdav.vue:685", "User specified path not accessible, status:", userPathResponse.status);
                    uni.showModal({
                      title: "路径提示",
                      content: `指定的路径 "${userPath}" 不存在或无权限访问，将使用服务器根路径。`,
                      showCancel: false,
                      confirmText: "确定"
                    });
                  }
                } catch (pathError) {
                  formatAppLog("log", "at pages/webdav/webdav.vue:694", "User specified path test failed:", pathError.message);
                  uni.showModal({
                    title: "路径提示",
                    content: `指定的路径 "${userPath}" 不存在或无权限访问，将使用服务器根路径。`,
                    showCancel: false,
                    confirmText: "确定"
                  });
                }
              }
              connectionSuccessful = true;
            } else {
              throw new Error(`WebDAV server root not accessible: ${rootTestResponse.status}`);
            }
          } catch (error) {
            formatAppLog("log", "at pages/webdav/webdav.vue:709", "WebDAV connection test failed:", error.message);
            throw error;
          }
          if (connectionSuccessful) {
            this.saveConfig();
            this.isConnected = true;
            this.currentPath = actualRootPath;
            await this.loadFileList();
            uni.showToast({
              title: "连接成功",
              icon: "success"
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/webdav/webdav.vue:731", "连接失败:", error);
          let errorMessage = "连接失败";
          if (error.message.includes("401") || error.message.includes("Authentication failed")) {
            errorMessage = "用户名或密码错误，请检查认证信息";
          } else if (error.message.includes("403")) {
            errorMessage = "访问被拒绝，请检查：\n1. 用户名密码是否正确\n2. 账户是否有WebDAV权限\n3. 服务器路径是否正确";
          } else if (error.message.includes("404")) {
            errorMessage = "WebDAV路径不存在，请检查：\n1. 服务器地址是否正确\n2. 是否包含正确的WebDAV路径\n3. 服务器是否启用WebDAV";
          } else if (error.message.includes("timeout")) {
            errorMessage = "连接超时，请检查网络连接";
          } else if (error.message.includes("network")) {
            errorMessage = "网络连接失败";
          } else {
            errorMessage = error.message || "未知错误";
          }
          uni.showModal({
            title: "连接失败",
            content: errorMessage,
            showCancel: false,
            confirmText: "确定"
          });
        } finally {
          this.connecting = false;
        }
      },
      // 断开连接
      disconnect() {
        this.isConnected = false;
        this.webdavClient = null;
        this.fileList = [];
        this.currentPath = "/";
        uni.showToast({
          title: "已断开连接",
          icon: "none"
        });
      },
      // 加载文件列表
      async loadFileList() {
        if (!this.webdavClient)
          return;
        this.loading = true;
        try {
          const files = await this.webdavClient.readDir(this.currentPath);
          this.fileList = files.sort((a, b) => {
            if (a.isDirectory && !b.isDirectory)
              return -1;
            if (!a.isDirectory && b.isDirectory)
              return 1;
            return a.name.localeCompare(b.name);
          });
          if (this.fileList.length === 0 && this.currentPath !== "/") {
            formatAppLog("log", "at pages/webdav/webdav.vue:790", "当前目录为空:", this.currentPath);
          }
        } catch (error) {
          formatAppLog("error", "at pages/webdav/webdav.vue:794", "加载文件列表失败:", error);
          let errorMessage = "加载失败";
          if (error.message.includes("403")) {
            errorMessage = "没有访问权限";
          } else if (error.message.includes("404")) {
            errorMessage = "目录不存在";
          } else if (error.message.includes("timeout")) {
            errorMessage = "请求超时";
          } else {
            errorMessage = error.message || "未知错误";
          }
          uni.showModal({
            title: "加载失败",
            content: errorMessage,
            showCancel: true,
            cancelText: "返回上级",
            confirmText: "重试",
            success: (res) => {
              if (res.confirm) {
                setTimeout(() => this.loadFileList(), 1e3);
              } else if (res.cancel && this.currentPath !== "/") {
                this.goBack();
              }
            }
          });
        } finally {
          this.loading = false;
        }
      },
      // 处理文件点击
      handleFileClick(file) {
        if (file.isDirectory) {
          this.currentPath = file.path;
          this.loadFileList();
        } else {
          this.showFileActions(file);
        }
      },
      // 返回上级目录
      goBack() {
        const pathParts = this.currentPath.split("/").filter((p) => p);
        if (pathParts.length > 0) {
          pathParts.pop();
          this.currentPath = "/" + pathParts.join("/");
        } else {
          this.currentPath = "/";
        }
        this.loadFileList();
      },
      // 导航到指定路径
      navigateToPath(segmentIndex) {
        if (segmentIndex === 0) {
          this.currentPath = "/";
        } else {
          const segments = this.pathSegments.slice(1, segmentIndex + 1);
          this.currentPath = "/" + segments.map((s) => s.name).join("/");
        }
        this.loadFileList();
      },
      // 显示文件操作菜单
      showFileActions(file) {
        const actions = [];
        if (!file.isDirectory) {
          actions.push("下载");
        }
        actions.push("重命名", "删除");
        uni.showActionSheet({
          itemList: actions,
          success: (res) => {
            const action = actions[res.tapIndex];
            this.handleFileAction(file, action);
          }
        });
      },
      // 处理文件操作
      async handleFileAction(file, action) {
        switch (action) {
          case "下载":
            await this.downloadFile(file);
            break;
          case "重命名":
            this.showRenameDialog(file);
            break;
          case "删除":
            this.confirmDelete(file);
            break;
        }
      },
      // 下载文件
      async downloadFile(file) {
        this.showProgressDialog("下载文件", "准备下载...");
        try {
          const fileData = await this.webdavClient.downloadFile(file.path, (received, total) => {
            if (total > 0) {
              this.updateProgress(
                Math.round(received / total * 100),
                `${this.formatFileSize(received)} / ${this.formatFileSize(total)}`
              );
            }
          });
          await this.saveFileToLocal(file.name, fileData);
          this.hideProgressDialog();
          uni.showToast({
            title: "下载完成",
            icon: "success"
          });
        } catch (error) {
          this.hideProgressDialog();
          formatAppLog("error", "at pages/webdav/webdav.vue:918", "下载失败:", error);
          uni.showToast({
            title: `下载失败: ${error.message}`,
            icon: "none"
          });
        }
      },
      // 保存文件到本地
      async saveFileToLocal(fileName, fileData) {
        return new Promise((resolve, reject) => {
          const downloadPath = plus.io.convertLocalFileSystemURL("_downloads/") + fileName;
          plus.io.requestFileSystem(plus.io.PUBLIC_DOWNLOADS, (fs) => {
            fs.root.getFile(fileName, { create: true }, (fileEntry) => {
              fileEntry.createWriter((writer) => {
                writer.onwriteend = () => {
                  formatAppLog("log", "at pages/webdav/webdav.vue:936", "文件保存成功:", downloadPath);
                  resolve(downloadPath);
                };
                writer.onerror = reject;
                writer.write(fileData);
              }, reject);
            }, reject);
          }, reject);
        });
      },
      // 显示创建文件夹对话框
      showCreateFolderDialog() {
        uni.showModal({
          title: "新建文件夹",
          editable: true,
          placeholderText: "请输入文件夹名称",
          success: async (res) => {
            if (res.confirm && res.content) {
              await this.createFolder(res.content.trim());
            }
          }
        });
      },
      // 创建文件夹
      async createFolder(folderName) {
        if (!folderName)
          return;
        if (!/^[^<>:"/\\|?*]+$/.test(folderName)) {
          uni.showToast({
            title: "文件夹名称包含非法字符",
            icon: "none"
          });
          return;
        }
        const folderPath = this.currentPath + (this.currentPath.endsWith("/") ? "" : "/") + folderName;
        uni.showLoading({
          title: "创建中..."
        });
        try {
          await this.webdavClient.mkdir(folderPath);
          await this.loadFileList();
          uni.hideLoading();
          uni.showToast({
            title: "文件夹创建成功",
            icon: "success"
          });
        } catch (error) {
          uni.hideLoading();
          formatAppLog("error", "at pages/webdav/webdav.vue:1009", "创建文件夹失败:", error);
          let errorMessage = "创建失败";
          if (error.message.includes("409")) {
            errorMessage = "文件夹已存在";
          } else if (error.message.includes("403")) {
            errorMessage = "没有创建权限";
          } else {
            errorMessage = error.message || "未知错误";
          }
          uni.showModal({
            title: "创建失败",
            content: errorMessage,
            showCancel: false,
            confirmText: "确定"
          });
        }
      },
      // 显示上传对话框
      showUploadDialog() {
        plus.io.requestFileSystem(plus.io.PUBLIC_DOCUMENTS, (fs) => {
          fs.root.createReader().readEntries((entries) => {
            const fileList = entries.filter((entry) => !entry.isDirectory).map((entry) => entry.name);
            if (fileList.length === 0) {
              uni.showToast({
                title: "没有找到可上传的文件",
                icon: "none"
              });
              return;
            }
            uni.showActionSheet({
              itemList: fileList,
              success: async (res) => {
                const selectedFile = entries[res.tapIndex];
                await this.uploadFileFromPath(selectedFile.fullPath, selectedFile.name);
              }
            });
          });
        });
      },
      // 从文件路径上传文件
      async uploadFileFromPath(filePath, fileName) {
        const remotePath = this.currentPath + (this.currentPath.endsWith("/") ? "" : "/") + fileName;
        this.showProgressDialog("上传文件", "准备上传...");
        try {
          const fileData = await this.readFileData(filePath);
          await this.webdavClient.uploadFile(remotePath, fileData, (sent, total) => {
            if (total > 0) {
              this.updateProgress(
                Math.round(sent / total * 100),
                `${this.formatFileSize(sent)} / ${this.formatFileSize(total)}`
              );
            }
          });
          await this.loadFileList();
          this.hideProgressDialog();
          uni.showToast({
            title: "上传完成",
            icon: "success"
          });
        } catch (error) {
          this.hideProgressDialog();
          formatAppLog("error", "at pages/webdav/webdav.vue:1114", "上传失败:", error);
          uni.showToast({
            title: `上传失败: ${error.message}`,
            icon: "none"
          });
        }
      },
      // 读取文件数据
      async readFileData(filePath) {
        return new Promise((resolve, reject) => {
          plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
            entry.file((file) => {
              const reader = new plus.io.FileReader();
              reader.onloadend = (e) => {
                resolve(e.target.result);
              };
              reader.onerror = reject;
              reader.readAsArrayBuffer(file);
            }, reject);
          }, reject);
        });
      },
      // 显示重命名对话框
      showRenameDialog(file) {
        uni.showModal({
          title: "重命名",
          editable: true,
          content: file.name,
          success: async (res) => {
            if (res.confirm && res.content && res.content !== file.name) {
              await this.renameFile(file, res.content.trim());
            }
          }
        });
      },
      // 重命名文件
      async renameFile(file, newName) {
        if (!newName)
          return;
        const newPath = file.path.substring(0, file.path.lastIndexOf("/") + 1) + newName;
        try {
          await this.webdavClient.rename(file.path, newPath);
          await this.loadFileList();
          uni.showToast({
            title: "重命名成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/webdav/webdav.vue:1195", "重命名失败:", error);
          uni.showToast({
            title: `重命名失败: ${error.message}`,
            icon: "none"
          });
        }
      },
      // 确认删除
      confirmDelete(file) {
        uni.showModal({
          title: "确认删除",
          content: `确定要删除 "${file.name}" 吗？`,
          success: async (res) => {
            if (res.confirm) {
              await this.deleteFile(file);
            }
          }
        });
      },
      // 删除文件
      async deleteFile(file) {
        try {
          await this.webdavClient.remove(file.path);
          await this.loadFileList();
          uni.showToast({
            title: "删除成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/webdav/webdav.vue:1227", "删除失败:", error);
          uni.showToast({
            title: `删除失败: ${error.message}`,
            icon: "none"
          });
        }
      },
      // 显示进度对话框
      showProgressDialog(title, text) {
        this.showProgress = true;
        this.progressTitle = title;
        this.progressText = text;
        this.progressPercent = 0;
        this.canCancelProgress = false;
      },
      // 更新进度
      updateProgress(percent, text) {
        this.progressPercent = percent;
        this.progressText = text;
      },
      // 隐藏进度对话框
      hideProgressDialog() {
        this.showProgress = false;
      },
      // 取消操作
      cancelOperation() {
        this.hideProgressDialog();
      },
      // 获取文件图标
      getFileIcon(fileName) {
        var _a;
        const ext = (_a = fileName.split(".").pop()) == null ? void 0 : _a.toLowerCase();
        const iconMap = {
          "jpg": "🖼️",
          "jpeg": "🖼️",
          "png": "🖼️",
          "gif": "🖼️",
          "bmp": "🖼️",
          "mp4": "🎬",
          "avi": "🎬",
          "mov": "🎬",
          "wmv": "🎬",
          "flv": "🎬",
          "mp3": "🎵",
          "wav": "🎵",
          "flac": "🎵",
          "aac": "🎵",
          "pdf": "📄",
          "doc": "📄",
          "docx": "📄",
          "txt": "📄",
          "zip": "📦",
          "rar": "📦",
          "7z": "📦",
          "tar": "📦",
          "js": "📜",
          "html": "📜",
          "css": "📜",
          "json": "📜"
        };
        return iconMap[ext] || "📄";
      },
      // 格式化文件大小
      formatFileSize(bytes) {
        if (bytes === 0)
          return "0 B";
        const k = 1024;
        const sizes = ["B", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
      },
      // 格式化日期
      formatDate(date) {
        if (!date)
          return "";
        const d = new Date(date);
        return d.toLocaleDateString() + " " + d.toLocaleTimeString();
      },
      // 检查网络状态
      checkNetworkStatus() {
        uni.getNetworkType({
          success: (res) => {
            if (res.networkType === "none") {
              uni.showToast({
                title: "网络连接不可用",
                icon: "none",
                duration: 3e3
              });
            }
          }
        });
      },
      // 显示操作确认对话框
      showConfirmDialog(title, content) {
        return new Promise((resolve) => {
          uni.showModal({
            title,
            content,
            success: (res) => {
              resolve(res.confirm);
            }
          });
        });
      },
      // 显示输入对话框
      showInputDialog(title, placeholder = "", defaultValue = "") {
        return new Promise((resolve) => {
          uni.showModal({
            title,
            editable: true,
            placeholderText: placeholder,
            content: defaultValue,
            success: (res) => {
              var _a;
              if (res.confirm) {
                resolve(((_a = res.content) == null ? void 0 : _a.trim()) || "");
              } else {
                resolve(null);
              }
            }
          });
        });
      },
      // 重试操作
      async retryOperation(operation, maxRetries = 3) {
        let retries = 0;
        while (retries < maxRetries) {
          try {
            return await operation();
          } catch (error) {
            retries++;
            if (retries >= maxRetries) {
              throw error;
            }
            await new Promise((resolve) => setTimeout(resolve, 1e3 * retries));
          }
        }
      },
      // 显示服务器模板选择
      showServerTemplates() {
        const templates = [
          "Nextcloud (需要替换域名和用户名)",
          "ownCloud (需要替换域名)",
          "通用WebDAV (需要替换域名)",
          "本地服务器 (需要替换IP)",
          "自定义输入"
        ];
        uni.showActionSheet({
          itemList: templates,
          success: (res) => {
            const templateUrls = [
              "https://your-domain.com/remote.php/dav/files/username/",
              "https://your-domain.com/remote.php/webdav/",
              "https://your-domain.com/webdav/",
              "http://*************:8080/webdav/",
              ""
            ];
            if (res.tapIndex < templateUrls.length - 1) {
              this.config.serverUrl = templateUrls[res.tapIndex];
              uni.showToast({
                title: "已填入模板，请修改为实际地址",
                icon: "none",
                duration: 3e3
              });
            }
          }
        });
      }
    }
  };
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "webdav-container" }, [
      vue.createCommentVNode(" 连接配置区域 "),
      !$data.isConnected ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "config-section"
      }, [
        vue.createElementVNode("view", { class: "config-header" }, [
          vue.createElementVNode("text", { class: "config-title" }, "WebDAV云盘连接"),
          vue.createElementVNode("text", { class: "config-subtitle" }, "请配置您的WebDAV服务器信息")
        ]),
        vue.createElementVNode("view", { class: "config-form" }, [
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "服务器地址"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $data.config.serverUrl = $event),
                placeholder: "https://cloud.example.com/remote.php/dav/files/username/",
                type: "text"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.config.serverUrl]
            ]),
            vue.createElementVNode("view", { class: "form-help" }, [
              vue.createElementVNode("text", { class: "help-text" }, "常见格式示例："),
              vue.createElementVNode("text", { class: "help-example" }, "• Nextcloud: https://your-domain.com/remote.php/dav/files/username/"),
              vue.createElementVNode("text", { class: "help-example" }, "• ownCloud: https://your-domain.com/remote.php/webdav/"),
              vue.createElementVNode("text", { class: "help-example" }, "• 通用WebDAV: https://your-domain.com/webdav/"),
              vue.createElementVNode("text", { class: "help-example" }, "• 本地服务器: http://*************:8080/webdav/"),
              vue.createElementVNode("button", {
                class: "help-btn",
                onClick: _cache[1] || (_cache[1] = (...args) => $options.showServerTemplates && $options.showServerTemplates(...args))
              }, "选择常用服务器模板")
            ])
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "用户名"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $data.config.username = $event),
                placeholder: "请输入用户名",
                type: "text"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.config.username]
            ])
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "密码"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.config.password = $event),
                placeholder: "请输入密码",
                type: "password"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.config.password]
            ])
          ]),
          vue.createElementVNode("view", { class: "form-item" }, [
            vue.createElementVNode("text", { class: "form-label" }, "根路径 (可选)"),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $data.config.rootPath = $event),
                placeholder: "/",
                type: "text"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $data.config.rootPath]
            ])
          ]),
          vue.createElementVNode("view", { class: "form-actions" }, [
            vue.createElementVNode("button", {
              class: "connect-btn",
              onClick: _cache[5] || (_cache[5] = (...args) => $options.connectToWebDAV && $options.connectToWebDAV(...args)),
              disabled: $data.connecting || !$options.isConfigValid
            }, [
              $data.connecting ? (vue.openBlock(), vue.createElementBlock("text", { key: 0 }, "连接中...")) : (vue.openBlock(), vue.createElementBlock("text", { key: 1 }, "连接"))
            ], 8, ["disabled"])
          ])
        ])
      ])) : (vue.openBlock(), vue.createElementBlock(
        vue.Fragment,
        { key: 1 },
        [
          vue.createCommentVNode(" 文件浏览区域 "),
          vue.createElementVNode("view", { class: "file-browser" }, [
            vue.createCommentVNode(" 工具栏 "),
            vue.createElementVNode("view", { class: "toolbar" }, [
              vue.createElementVNode("view", { class: "path-bar" }, [
                vue.createElementVNode(
                  "text",
                  { class: "current-path" },
                  vue.toDisplayString($data.currentPath),
                  1
                  /* TEXT */
                )
              ]),
              vue.createElementVNode("view", { class: "toolbar-actions" }, [
                vue.createElementVNode("button", {
                  class: "action-btn",
                  onClick: _cache[6] || (_cache[6] = (...args) => $options.showCreateFolderDialog && $options.showCreateFolderDialog(...args))
                }, " 📁 新建文件夹 "),
                vue.createElementVNode("button", {
                  class: "action-btn",
                  onClick: _cache[7] || (_cache[7] = (...args) => $options.showUploadDialog && $options.showUploadDialog(...args))
                }, " 📤 上传文件 "),
                vue.createElementVNode("button", {
                  class: "action-btn disconnect-btn",
                  onClick: _cache[8] || (_cache[8] = (...args) => $options.disconnect && $options.disconnect(...args))
                }, " 🔌 断开连接 ")
              ])
            ]),
            vue.createCommentVNode(" 导航栏 "),
            $options.pathSegments.length > 1 ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 0,
              class: "breadcrumb"
            }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($options.pathSegments, (segment, index) => {
                  return vue.openBlock(), vue.createElementBlock("text", {
                    key: index,
                    class: "breadcrumb-item",
                    onClick: ($event) => $options.navigateToPath(index)
                  }, [
                    vue.createTextVNode(
                      vue.toDisplayString(segment.name) + " ",
                      1
                      /* TEXT */
                    ),
                    index < $options.pathSegments.length - 1 ? (vue.openBlock(), vue.createElementBlock("text", {
                      key: 0,
                      class: "breadcrumb-separator"
                    }, ">")) : vue.createCommentVNode("v-if", true)
                  ], 8, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ])) : vue.createCommentVNode("v-if", true),
            vue.createCommentVNode(" 加载状态 "),
            $data.loading ? (vue.openBlock(), vue.createElementBlock("view", {
              key: 1,
              class: "loading-section"
            }, [
              vue.createElementVNode("view", { class: "spinner" }),
              vue.createElementVNode("text", { class: "loading-text" }, "加载中...")
            ])) : (vue.openBlock(), vue.createElementBlock(
              vue.Fragment,
              { key: 2 },
              [
                vue.createCommentVNode(" 文件列表 "),
                vue.createElementVNode("scroll-view", {
                  class: "file-list",
                  "scroll-y": "true"
                }, [
                  vue.createCommentVNode(" 返回上级目录 "),
                  $data.currentPath !== "/" ? (vue.openBlock(), vue.createElementBlock("view", {
                    key: 0,
                    class: "file-item directory-item",
                    onClick: _cache[9] || (_cache[9] = (...args) => $options.goBack && $options.goBack(...args))
                  }, [
                    vue.createElementVNode("view", { class: "file-icon" }, "📁"),
                    vue.createElementVNode("view", { class: "file-info" }, [
                      vue.createElementVNode("text", { class: "file-name" }, ".."),
                      vue.createElementVNode("text", { class: "file-details" }, "返回上级目录")
                    ])
                  ])) : vue.createCommentVNode("v-if", true),
                  vue.createCommentVNode(" 文件和文件夹列表 "),
                  (vue.openBlock(true), vue.createElementBlock(
                    vue.Fragment,
                    null,
                    vue.renderList($data.fileList, (file) => {
                      return vue.openBlock(), vue.createElementBlock("view", {
                        key: file.path,
                        class: vue.normalizeClass(["file-item", { "directory-item": file.isDirectory }]),
                        onClick: ($event) => $options.handleFileClick(file),
                        onLongpress: ($event) => $options.showFileActions(file)
                      }, [
                        vue.createElementVNode("view", { class: "file-icon" }, [
                          file.isDirectory ? (vue.openBlock(), vue.createElementBlock("text", { key: 0 }, "📁")) : (vue.openBlock(), vue.createElementBlock(
                            "text",
                            { key: 1 },
                            vue.toDisplayString($options.getFileIcon(file.name)),
                            1
                            /* TEXT */
                          ))
                        ]),
                        vue.createElementVNode("view", { class: "file-info" }, [
                          vue.createElementVNode(
                            "text",
                            { class: "file-name" },
                            vue.toDisplayString(file.name),
                            1
                            /* TEXT */
                          ),
                          vue.createElementVNode("text", { class: "file-details" }, [
                            !file.isDirectory ? (vue.openBlock(), vue.createElementBlock(
                              "text",
                              { key: 0 },
                              vue.toDisplayString($options.formatFileSize(file.size)),
                              1
                              /* TEXT */
                            )) : vue.createCommentVNode("v-if", true),
                            vue.createElementVNode(
                              "text",
                              { class: "file-date" },
                              vue.toDisplayString($options.formatDate(file.lastModified)),
                              1
                              /* TEXT */
                            )
                          ])
                        ]),
                        vue.createElementVNode("view", {
                          class: "file-actions",
                          onClick: vue.withModifiers(($event) => $options.showFileActions(file), ["stop"])
                        }, [
                          vue.createElementVNode("text", { class: "action-icon" }, "⋮")
                        ], 8, ["onClick"])
                      ], 42, ["onClick", "onLongpress"]);
                    }),
                    128
                    /* KEYED_FRAGMENT */
                  )),
                  vue.createCommentVNode(" 空状态 "),
                  $data.fileList.length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                    key: 1,
                    class: "empty-state"
                  }, [
                    vue.createElementVNode("text", { class: "empty-icon" }, "📂"),
                    vue.createElementVNode("text", { class: "empty-text" }, "此文件夹为空")
                  ])) : vue.createCommentVNode("v-if", true)
                ])
              ],
              2112
              /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
            ))
          ])
        ],
        2112
        /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
      )),
      vue.createCommentVNode(" 进度提示 "),
      $data.showProgress ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 2,
        class: "progress-overlay"
      }, [
        vue.createElementVNode("view", { class: "progress-dialog" }, [
          vue.createElementVNode(
            "text",
            { class: "progress-title" },
            vue.toDisplayString($data.progressTitle),
            1
            /* TEXT */
          ),
          vue.createElementVNode("view", { class: "progress-bar" }, [
            vue.createElementVNode(
              "view",
              {
                class: "progress-fill",
                style: vue.normalizeStyle({ width: $data.progressPercent + "%" })
              },
              null,
              4
              /* STYLE */
            )
          ]),
          vue.createElementVNode(
            "text",
            { class: "progress-text" },
            vue.toDisplayString($data.progressText),
            1
            /* TEXT */
          ),
          $data.canCancelProgress ? (vue.openBlock(), vue.createElementBlock("button", {
            key: 0,
            class: "cancel-btn",
            onClick: _cache[10] || (_cache[10] = (...args) => $options.cancelOperation && $options.cancelOperation(...args))
          }, " 取消 ")) : vue.createCommentVNode("v-if", true)
        ])
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesWebdavWebdav = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$2], ["__scopeId", "data-v-8b618efa"], ["__file", "D:/phpStudy/WWW/jiami-app/pages/webdav/webdav.vue"]]);
  class SQLiteParser {
    constructor() {
      this.buffer = null;
      this.view = null;
    }
    /**
     * 加载SQLite文件
     * @param {File|ArrayBuffer} file 文件对象或ArrayBuffer
     */
    async loadFile(file) {
      try {
        let arrayBuffer;
        if (file instanceof File) {
          arrayBuffer = await file.arrayBuffer();
        } else if (file instanceof ArrayBuffer) {
          arrayBuffer = file;
        } else {
          throw new Error("不支持的文件类型");
        }
        this.buffer = arrayBuffer;
        this.view = new DataView(arrayBuffer);
        const header = new Uint8Array(arrayBuffer, 0, 16);
        const expectedHeader = new Uint8Array([
          83,
          81,
          76,
          105,
          116,
          101,
          32,
          102,
          111,
          114,
          109,
          97,
          116,
          32,
          51,
          0
        ]);
        for (let i = 0; i < 16; i++) {
          if (header[i] !== expectedHeader[i]) {
            throw new Error("不是有效的SQLite文件");
          }
        }
        formatAppLog("log", "at utils/sqlite-parser.js:48", "SQLite文件加载成功");
        return true;
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-parser.js:51", "加载SQLite文件失败:", error);
        throw error;
      }
    }
    /**
     * 读取字符串（以null结尾）
     * @param {number} offset 偏移量
     * @returns {string}
     */
    readString(offset) {
      const bytes = [];
      let i = offset;
      while (i < this.buffer.byteLength) {
        const byte = this.view.getUint8(i);
        if (byte === 0)
          break;
        bytes.push(byte);
        i++;
      }
      return new TextDecoder("utf-8").decode(new Uint8Array(bytes));
    }
    /**
     * 解析变长整数
     * @param {number} offset 偏移量
     * @returns {{value: number, nextOffset: number}}
     */
    readVarint(offset) {
      let value = 0;
      let shift = 0;
      let nextOffset = offset;
      for (let i = 0; i < 9; i++) {
        const byte = this.view.getUint8(nextOffset++);
        if (i === 8) {
          value |= byte << shift;
          break;
        } else {
          value |= (byte & 127) << shift;
          if ((byte & 128) === 0)
            break;
          shift += 7;
        }
      }
      return { value, nextOffset };
    }
    /**
     * 模拟查询encrypted_images表
     * 这是一个简化的实现，实际应该解析SQLite的页面结构
     * 由于完整的SQLite解析非常复杂，这里提供一个基于已知数据结构的解析方法
     */
    async queryImages() {
      try {
        const mockData = [
          {
            id: 1,
            file_path: "../data/file/2025-07-31/IMG20200312161551.jpg",
            password: "P1Dd7d036UoYVm0y853c2c49bfa3846606ab595b1c844665e1dedcff9e7dd2852c0d6a781edd1195",
            iv: "2025-07-30 18:43:41",
            sha1_hash: "3c2c49bfa3846606ab595b1c844665e1dedcff9e7dd2852c0d6a781edd1195",
            created_at: "2025-07-30 18:43:41",
            txt: "",
            capture_date: "2020:03:12 16:15:51",
            file_size_mb: 2.5,
            image_width: 1920,
            image_height: 1080,
            gps_latitude: 39.9042,
            gps_longitude: 116.4074
          },
          {
            id: 2,
            file_path: "../data/file/2025-07-31/IMG20200312161548.jpg",
            password: "BIcTX6kT9-IW-tOO694ccebc2b700d91f6ce606efb0782a61d1caa9b543ace2788555c9783946eec",
            iv: "2025-07-30 18:43:41",
            sha1_hash: "694ccebc2b700d91f6ce606efb0782a61d1caa9b543ace2788555c9783946eec",
            created_at: "2025-07-30 18:43:41",
            txt: "",
            capture_date: "2020:03:12 16:15:48",
            file_size_mb: 2.3,
            image_width: 1920,
            image_height: 1080,
            gps_latitude: 39.9042,
            gps_longitude: 116.4074
          },
          {
            id: 3,
            file_path: "../data/file/2025-07-31/IMG20190711165840.jpg",
            password: "uG1EQQHCUFVI_M8a7029d6d3cc7b6f76d789583e692ef6f10cd2389296dad0f477de6f19e161b1ac",
            iv: "2025-07-30 18:43:38",
            sha1_hash: "a7029d6d3cc7b6f76d789583e692ef6f10cd2389296dad0f477de6f19e161b1ac",
            created_at: "2025-07-30 18:43:38",
            txt: "",
            capture_date: "2019:07:11 16:58:40",
            file_size_mb: 1.8,
            image_width: 1920,
            image_height: 1080,
            gps_latitude: null,
            gps_longitude: null
          },
          {
            id: 4,
            file_path: "../data/file/2025-07-31/IMG20190621141002.jpg",
            password: "lkW4dKvjULCKkW7y8571c8692d1ded7e9060b167fd86adc37c119e9b246314b86198c2c14f1cdd64",
            iv: "2025-07-30 18:43:35",
            sha1_hash: "2019:06:21 14:10:02",
            created_at: "2025-07-30 18:43:35",
            txt: "",
            capture_date: "2019:06:21 14:10:02",
            file_size_mb: 2.1,
            image_width: 1920,
            image_height: 1080,
            gps_latitude: null,
            gps_longitude: null
          },
          {
            id: 5,
            file_path: "../data/file/2025-07-31/IMG_0045.HEIC",
            password: "yRsxtYTMQ-ZbzyW72192bdfb74a64b3b76cd7d67dd07527c9c2943290cc14ee51056a877b8626a60",
            iv: "2025-07-30 18:43:33",
            sha1_hash: "",
            created_at: "2025-07-30 18:43:33",
            txt: "",
            capture_date: null,
            file_size_mb: 1.5,
            image_width: null,
            image_height: null,
            gps_latitude: null,
            gps_longitude: null
          },
          {
            id: 6,
            file_path: "../data/file/2025-07-31/IMG20200312162112.jpg",
            password: "fIsIZ3-aACs1yNeV2233bd7706b151a6273c32086e3456785176385d12700046ecc0216a3be12280",
            iv: "2025-07-30 18:43:30",
            sha1_hash: "2020:03:12 16:21:12",
            created_at: "2025-07-30 18:43:30",
            txt: "",
            capture_date: "2020:03:12 16:21:12",
            file_size_mb: 2.8,
            image_width: 1920,
            image_height: 1080,
            gps_latitude: null,
            gps_longitude: null
          }
        ];
        return mockData;
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-parser.js:205", "查询图片数据失败:", error);
        throw error;
      }
    }
    /**
     * 查询视频数据
     */
    async queryVideos() {
      try {
        const mockData = [
          {
            id: 1,
            sha1_hash: "17a603598b0bb7733e783e60ddc677320c65b81f",
            original_filename: "video_20250205_160135(1).mp4",
            m3u8_path: "../data/video/2025-07-30/17a603598b0bb7733e783e60ddc677320c65b81f/playlist.m3u8",
            encryption_date: "2025-07-30 12:07:05",
            password: "bda91770511211f4b1c9f9fc72b160c0fde00c55c3a5c600cf80a5a0513fc821",
            iv: "2025-07-30 20:06:59",
            file_created_date: null,
            file_size_mb: 15.2,
            tag: "",
            txt: ""
          }
        ];
        return mockData;
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-parser.js:233", "查询视频数据失败:", error);
        throw error;
      }
    }
    /**
     * 查询文件数据
     */
    async queryFiles() {
      try {
        const mockData = [
          {
            id: 1,
            sha1_hash: "dd2259da3c23ebd71274f6a0b27a3996b8313485",
            original_filename: "video-decrypt-player.zip",
            file_path: "../data/other/2025-07-30/video-decrypt-player.zip",
            encryption_date: "2025-07-30 11:29:47",
            password: "ff013d99d2c86c66f0f3744b2205a59c35b19c486f2a2ee0ec62d66b",
            iv: "2025-07-30 19:29:45",
            file_created_date: null,
            file_size_mb: 0.8,
            tag: "",
            txt: ""
          }
        ];
        return mockData;
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-parser.js:261", "查询文件数据失败:", error);
        throw error;
      }
    }
  }
  class SQLiteRealParser {
    constructor() {
      this.db = null;
      this.SQL = null;
    }
    /**
     * 初始化sql.js库
     */
    async initSQL() {
      if (this.SQL)
        return;
      try {
        if (typeof window.initSqlJs === "undefined") {
          throw new Error("sql.js库未加载，请先引入sql.js");
        }
        this.SQL = await window.initSqlJs({
          // 可以指定wasm文件的路径
          locateFile: (file) => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
        });
        formatAppLog("log", "at utils/sqlite-real.js:32", "sql.js初始化成功");
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:34", "sql.js初始化失败:", error);
        throw error;
      }
    }
    /**
     * 加载SQLite文件
     * @param {File|ArrayBuffer} file 文件对象或ArrayBuffer
     */
    async loadFile(file) {
      try {
        await this.initSQL();
        let arrayBuffer;
        if (file instanceof File) {
          arrayBuffer = await file.arrayBuffer();
        } else if (file instanceof ArrayBuffer) {
          arrayBuffer = file;
        } else {
          throw new Error("不支持的文件类型");
        }
        const uint8Array = new Uint8Array(arrayBuffer);
        this.db = new this.SQL.Database(uint8Array);
        formatAppLog("log", "at utils/sqlite-real.js:61", "SQLite数据库加载成功");
        await this.validateDatabase();
        return true;
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:68", "加载SQLite文件失败:", error);
        throw error;
      }
    }
    /**
     * 验证数据库结构
     */
    async validateDatabase() {
      try {
        const tables = this.db.exec("SELECT name FROM sqlite_master WHERE type='table'");
        const tableNames = tables[0] ? tables[0].values.map((row) => row[0]) : [];
        const requiredTables = ["encrypted_images", "encrypted_videos", "encrypted_files"];
        const missingTables = requiredTables.filter((table) => !tableNames.includes(table));
        if (missingTables.length > 0) {
          formatAppLog("warn", "at utils/sqlite-real.js:86", "缺少表:", missingTables);
        }
        formatAppLog("log", "at utils/sqlite-real.js:89", "数据库表:", tableNames);
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:91", "验证数据库结构失败:", error);
      }
    }
    /**
     * 执行SQL查询
     * @param {string} sql SQL查询语句
     * @returns {Array} 查询结果
     */
    query(sql) {
      if (!this.db) {
        throw new Error("数据库未加载");
      }
      try {
        const results = this.db.exec(sql);
        if (results.length === 0) {
          return [];
        }
        const columns = results[0].columns;
        const values = results[0].values;
        return values.map((row) => {
          const obj = {};
          columns.forEach((col, index) => {
            obj[col] = row[index];
          });
          return obj;
        });
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:123", "SQL查询失败:", error);
        throw error;
      }
    }
    /**
     * 查询图片数据
     */
    async queryImages() {
      try {
        const sql = `
        SELECT 
          id, file_path, password, iv, sha1_hash, created_at, 
          txt, capture_date, file_size_mb, image_width, image_height,
          gps_latitude, gps_longitude
        FROM encrypted_images 
        ORDER BY created_at DESC
      `;
        return this.query(sql);
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:144", "查询图片数据失败:", error);
        throw error;
      }
    }
    /**
     * 查询视频数据
     */
    async queryVideos() {
      try {
        const sql = `
        SELECT 
          id, sha1_hash, original_filename, m3u8_path, encryption_date,
          password, iv, file_created_date, file_size_mb, tag, txt
        FROM encrypted_videos 
        ORDER BY encryption_date DESC
      `;
        return this.query(sql);
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:164", "查询视频数据失败:", error);
        throw error;
      }
    }
    /**
     * 查询文件数据
     */
    async queryFiles() {
      try {
        const sql = `
        SELECT 
          id, sha1_hash, original_filename, file_path, encryption_date,
          password, iv, file_created_date, file_size_mb, tag, txt
        FROM encrypted_files 
        ORDER BY encryption_date DESC
      `;
        return this.query(sql);
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:184", "查询文件数据失败:", error);
        throw error;
      }
    }
    /**
     * 根据条件查询图片
     * @param {Object} conditions 查询条件
     */
    async queryImagesWithConditions(conditions = {}) {
      try {
        let sql = `
        SELECT 
          id, file_path, password, iv, sha1_hash, created_at, 
          txt, capture_date, file_size_mb, image_width, image_height,
          gps_latitude, gps_longitude
        FROM encrypted_images 
      `;
        const whereClauses = [];
        const params = [];
        if (conditions.dateFrom) {
          whereClauses.push("created_at >= ?");
          params.push(conditions.dateFrom);
        }
        if (conditions.dateTo) {
          whereClauses.push("created_at <= ?");
          params.push(conditions.dateTo);
        }
        if (conditions.tag) {
          whereClauses.push("txt LIKE ?");
          params.push(`%${conditions.tag}%`);
        }
        if (whereClauses.length > 0) {
          sql += " WHERE " + whereClauses.join(" AND ");
        }
        sql += " ORDER BY created_at DESC";
        if (conditions.limit) {
          sql += ` LIMIT ${conditions.limit}`;
        }
        return this.query(sql);
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:233", "条件查询图片失败:", error);
        throw error;
      }
    }
    /**
     * 获取统计信息
     */
    async getStatistics() {
      var _a, _b, _c, _d;
      try {
        const imageCount = ((_a = this.query("SELECT COUNT(*) as count FROM encrypted_images")[0]) == null ? void 0 : _a.count) || 0;
        const videoCount = ((_b = this.query("SELECT COUNT(*) as count FROM encrypted_videos")[0]) == null ? void 0 : _b.count) || 0;
        const fileCount = ((_c = this.query("SELECT COUNT(*) as count FROM encrypted_files")[0]) == null ? void 0 : _c.count) || 0;
        const totalSize = ((_d = this.query(`
        SELECT 
          (SELECT COALESCE(SUM(file_size_mb), 0) FROM encrypted_images) +
          (SELECT COALESCE(SUM(file_size_mb), 0) FROM encrypted_videos) +
          (SELECT COALESCE(SUM(file_size_mb), 0) FROM encrypted_files) as total_size
      `)[0]) == null ? void 0 : _d.total_size) || 0;
        return {
          imageCount,
          videoCount,
          fileCount,
          totalCount: imageCount + videoCount + fileCount,
          totalSizeMB: totalSize
        };
      } catch (error) {
        formatAppLog("error", "at utils/sqlite-real.js:262", "获取统计信息失败:", error);
        throw error;
      }
    }
    /**
     * 关闭数据库连接
     */
    close() {
      if (this.db) {
        this.db.close();
        this.db = null;
      }
    }
  }
  var lookup = [
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    62,
    0,
    62,
    0,
    63,
    52,
    53,
    54,
    55,
    56,
    57,
    58,
    59,
    60,
    61,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    21,
    22,
    23,
    24,
    25,
    0,
    0,
    0,
    0,
    63,
    0,
    26,
    27,
    28,
    29,
    30,
    31,
    32,
    33,
    34,
    35,
    36,
    37,
    38,
    39,
    40,
    41,
    42,
    43,
    44,
    45,
    46,
    47,
    48,
    49,
    50,
    51
  ];
  function base64Decode(source, target) {
    var sourceLength = source.length;
    var paddingLength = source[sourceLength - 2] === "=" ? 2 : source[sourceLength - 1] === "=" ? 1 : 0;
    var tmp;
    var byteIndex = 0;
    var baseLength = sourceLength - paddingLength & 4294967292;
    for (var i = 0; i < baseLength; i += 4) {
      tmp = lookup[source.charCodeAt(i)] << 18 | lookup[source.charCodeAt(i + 1)] << 12 | lookup[source.charCodeAt(i + 2)] << 6 | lookup[source.charCodeAt(i + 3)];
      target[byteIndex++] = tmp >> 16 & 255;
      target[byteIndex++] = tmp >> 8 & 255;
      target[byteIndex++] = tmp & 255;
    }
    if (paddingLength === 1) {
      tmp = lookup[source.charCodeAt(i)] << 10 | lookup[source.charCodeAt(i + 1)] << 4 | lookup[source.charCodeAt(i + 2)] >> 2;
      target[byteIndex++] = tmp >> 8 & 255;
      target[byteIndex++] = tmp & 255;
    }
    if (paddingLength === 2) {
      tmp = lookup[source.charCodeAt(i)] << 2 | lookup[source.charCodeAt(i + 1)] >> 4;
      target[byteIndex++] = tmp & 255;
    }
  }
  const crypto = {
    getRandomValues(arr) {
      if (!(arr instanceof Int8Array || arr instanceof Uint8Array || arr instanceof Int16Array || arr instanceof Uint16Array || arr instanceof Int32Array || arr instanceof Uint32Array || arr instanceof Uint8ClampedArray)) {
        throw new Error("Expected an integer array");
      }
      if (arr.byteLength > 65536) {
        throw new Error("Can only request a maximum of 65536 bytes");
      }
      var crypto2 = requireNativePlugin("DCloud-Crypto");
      base64Decode(crypto2.getRandomValues(arr.byteLength), new Uint8Array(
        arr.buffer,
        arr.byteOffset,
        arr.byteLength
      ));
      return arr;
    }
  };
  async function generateSHA256(text) {
    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(text);
      const hashBuffer = await crypto.subtle.digest("SHA-256", data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
      return hashHex;
    } catch (error) {
      formatAppLog("error", "at utils/crypto-utils.js:20", "SHA256哈希生成失败:", error);
      throw error;
    }
  }
  function hexToArrayBuffer(hex) {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
    }
    return bytes.buffer;
  }
  async function decryptAES256GCM(encryptedDataHex, passwordHex, ivHex) {
    try {
      const encryptedData = hexToArrayBuffer(encryptedDataHex);
      const password = hexToArrayBuffer(passwordHex);
      const iv = hexToArrayBuffer(ivHex);
      const key = await crypto.subtle.importKey(
        "raw",
        password,
        { name: "AES-GCM" },
        false,
        ["decrypt"]
      );
      const decryptedData = await crypto.subtle.decrypt(
        {
          name: "AES-GCM",
          iv
        },
        key,
        encryptedData
      );
      return decryptedData;
    } catch (error) {
      formatAppLog("error", "at utils/crypto-utils.js:83", "AES解密失败:", error);
      throw error;
    }
  }
  function createBlobUrl(decryptedData, mimeType) {
    try {
      const blob = new Blob([decryptedData], { type: mimeType });
      return URL.createObjectURL(blob);
    } catch (error) {
      formatAppLog("error", "at utils/crypto-utils.js:99", "创建Blob URL失败:", error);
      throw error;
    }
  }
  function getMimeType(filename) {
    const ext = filename.toLowerCase().split(".").pop();
    const mimeTypes = {
      "jpg": "image/jpeg",
      "jpeg": "image/jpeg",
      "png": "image/png",
      "gif": "image/gif",
      "webp": "image/webp",
      "bmp": "image/bmp",
      "heic": "image/heic",
      "mp4": "video/mp4",
      "avi": "video/avi",
      "mov": "video/quicktime",
      "wmv": "video/x-ms-wmv",
      "flv": "video/x-flv",
      "pdf": "application/pdf",
      "doc": "application/msword",
      "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "xls": "application/vnd.ms-excel",
      "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "txt": "text/plain",
      "zip": "application/zip",
      "rar": "application/x-rar-compressed"
    };
    return mimeTypes[ext] || "application/octet-stream";
  }
  function getTodayDateString() {
    const today = /* @__PURE__ */ new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }
  async function generateFileUrl(filePath, serverDomain, accessKey, type = "file") {
    try {
      const dateStr = getTodayDateString();
      const hashStr = await generateSHA256(accessKey + dateStr);
      const pathParts = filePath.split("/");
      const filename = pathParts[pathParts.length - 1];
      const relativePath = pathParts.slice(2, -1).join("/");
      return `${serverDomain}/${type}/${relativePath}/${hashStr}/${filename}`;
    } catch (error) {
      formatAppLog("error", "at utils/crypto-utils.js:172", "生成文件URL失败:", error);
      throw error;
    }
  }
  async function generateThumbnailUrl(filePath, serverDomain, accessKey) {
    try {
      const url = await generateFileUrl(filePath, serverDomain, accessKey, "file");
      const lastDotIndex = url.lastIndexOf(".");
      if (lastDotIndex > -1) {
        return url.substring(0, lastDotIndex) + "_tagimg" + url.substring(lastDotIndex);
      }
      return url + "_tagimg";
    } catch (error) {
      formatAppLog("error", "at utils/crypto-utils.js:193", "生成缩略图URL失败:", error);
      throw error;
    }
  }
  const _sfc_main$1 = {
    data() {
      return {
        activeTab: "images",
        dbLoaded: false,
        settingsConfigured: false,
        serverDomain: "",
        accessKey: "",
        imageList: [],
        videoList: [],
        fileList: [],
        loading: false,
        dbParser: null,
        decryptedImageCache: /* @__PURE__ */ new Map(),
        // 缓存解密后的图片
        useRealParser: false
        // 是否使用真实的SQLite解析器
      };
    },
    onLoad() {
      this.loadSettings();
    },
    methods: {
      // 切换标签
      switchTab(tab) {
        this.activeTab = tab;
        if (this.settingsConfigured && this.dbLoaded) {
          this.loadData();
        }
      },
      // 从应用私有目录加载数据库
      async loadDatabaseFromPrivateDir() {
        try {
          this.loading = true;
          const dbPath = await this.getPrivateDirPath();
          formatAppLog("log", "at pages/browser/browser.vue:193", "数据库路径:", dbPath);
          const dbData = await this.readDatabaseFile(dbPath);
          await this.loadDatabase(dbData);
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:202", "加载数据库失败:", error);
          uni.showToast({
            title: `加载数据库失败: ${error.message}`,
            icon: "none"
          });
        } finally {
          this.loading = false;
        }
      },
      // 获取应用私有目录中的数据库路径
      async getPrivateDirPath() {
        return new Promise((resolve, reject) => {
          try {
            if (typeof plus !== "undefined") {
              plus.io.requestFileSystem(plus.io.PRIVATE_DOC, (fs) => {
                const dbPath = fs.root.toLocalURL() + "image_encryption.db";
                formatAppLog("log", "at pages/browser/browser.vue:221", "构建的数据库路径:", dbPath);
                resolve(dbPath);
              }, (error) => {
                formatAppLog("error", "at pages/browser/browser.vue:224", "获取私有目录失败:", error);
                reject(new Error("无法访问应用私有目录"));
              });
            } else {
              resolve("./doc/image_encryption.db");
            }
          } catch (error) {
            reject(error);
          }
        });
      },
      // 读取数据库文件
      async readDatabaseFile(dbPath) {
        return new Promise((resolve, reject) => {
          try {
            if (typeof plus !== "undefined") {
              plus.io.resolveLocalFileSystemURL(dbPath, (entry) => {
                entry.file((file) => {
                  const reader = new plus.io.FileReader();
                  reader.onloadend = (e) => {
                    formatAppLog("log", "at pages/browser/browser.vue:247", "数据库文件读取成功，大小:", e.target.result.byteLength);
                    resolve(e.target.result);
                  };
                  reader.onerror = (error) => {
                    formatAppLog("error", "at pages/browser/browser.vue:251", "读取文件失败:", error);
                    reject(new Error("读取数据库文件失败"));
                  };
                  reader.readAsArrayBuffer(file);
                }, (error) => {
                  formatAppLog("error", "at pages/browser/browser.vue:256", "获取文件对象失败:", error);
                  reject(new Error("获取数据库文件失败"));
                });
              }, (error) => {
                formatAppLog("error", "at pages/browser/browser.vue:260", "解析文件路径失败:", error);
                reject(new Error("数据库文件不存在，请确保文件位于应用私有目录 image_encryption.db"));
              });
            } else {
              fetch(dbPath).then((response) => {
                if (!response.ok) {
                  throw new Error("数据库文件不存在");
                }
                return response.arrayBuffer();
              }).then(resolve).catch(reject);
            }
          } catch (error) {
            reject(error);
          }
        });
      },
      // 加载数据库
      async loadDatabase(file) {
        try {
          this.loading = true;
          await this.parseSQLiteFile(file);
          this.dbLoaded = true;
          if (this.settingsConfigured) {
            await this.loadData();
          }
          uni.showToast({
            title: "数据库加载成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:299", "加载数据库失败:", error);
          uni.showToast({
            title: "加载数据库失败",
            icon: "none"
          });
        } finally {
          this.loading = false;
        }
      },
      // 解析SQLite文件
      async parseSQLiteFile(file) {
        try {
          if (this.useRealParser) {
            this.dbParser = new SQLiteRealParser();
          } else {
            this.dbParser = new SQLiteParser();
          }
          await this.dbParser.loadFile(file);
          formatAppLog("log", "at pages/browser/browser.vue:319", "SQLite文件解析成功，使用", this.useRealParser ? "真实" : "模拟", "解析器");
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:321", "解析SQLite文件失败:", error);
          throw error;
        }
      },
      // 加载设置
      loadSettings() {
        try {
          const domain = uni.getStorageSync("serverDomain");
          const key = uni.getStorageSync("accessKey");
          if (domain && key) {
            this.serverDomain = domain;
            this.accessKey = key;
            this.settingsConfigured = true;
          }
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:338", "加载设置失败:", error);
        }
      },
      // 保存设置
      saveSettings() {
        if (!this.serverDomain || !this.accessKey) {
          uni.showToast({
            title: "请填写完整设置",
            icon: "none"
          });
          return;
        }
        try {
          uni.setStorageSync("serverDomain", this.serverDomain);
          uni.setStorageSync("accessKey", this.accessKey);
          this.settingsConfigured = true;
          if (this.dbLoaded) {
            this.loadData();
          }
          uni.showToast({
            title: "设置保存成功",
            icon: "success"
          });
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:366", "保存设置失败:", error);
          uni.showToast({
            title: "保存设置失败",
            icon: "none"
          });
        }
      },
      // 加载数据
      async loadData() {
        if (!this.dbParser)
          return;
        this.loading = true;
        try {
          switch (this.activeTab) {
            case "images":
              await this.loadImages();
              break;
            case "videos":
              await this.loadVideos();
              break;
            case "files":
              await this.loadFiles();
              break;
          }
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:392", "加载数据失败:", error);
          uni.showToast({
            title: "加载数据失败",
            icon: "none"
          });
        } finally {
          this.loading = false;
        }
      },
      // 加载图片数据
      async loadImages() {
        try {
          const images = await this.dbParser.queryImages();
          this.imageList = [];
          for (const image of images) {
            try {
              const imageItem = {
                ...image,
                thumbnailUrl: "",
                // 先设为空，后续异步加载
                original_filename: this.getFilenameFromPath(image.file_path),
                loading: true
              };
              this.imageList.push(imageItem);
              this.loadThumbnail(imageItem);
            } catch (error) {
              formatAppLog("error", "at pages/browser/browser.vue:423", "处理图片失败:", image.file_path, error);
            }
          }
          formatAppLog("log", "at pages/browser/browser.vue:427", "图片列表加载完成:", this.imageList.length);
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:429", "加载图片数据失败:", error);
          throw error;
        }
      },
      // 加载缩略图
      async loadThumbnail(imageItem) {
        try {
          const thumbnailUrl = await generateThumbnailUrl(
            imageItem.file_path,
            this.serverDomain,
            this.accessKey
          );
          const response = await fetch(thumbnailUrl);
          if (!response.ok) {
            throw new Error(`下载缩略图失败: ${response.status}`);
          }
          const encryptedData = await response.arrayBuffer();
          const encryptedHex = Array.from(new Uint8Array(encryptedData)).map((b) => b.toString(16).padStart(2, "0")).join("");
          const decryptedData = await decryptAES256GCM(
            encryptedHex,
            imageItem.password,
            imageItem.iv
          );
          const mimeType = getMimeType(imageItem.file_path);
          const blobUrl = createBlobUrl(decryptedData, mimeType);
          imageItem.thumbnailUrl = blobUrl;
          imageItem.loading = false;
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:469", "加载缩略图失败:", imageItem.file_path, error);
          imageItem.loading = false;
          imageItem.thumbnailUrl = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Yqg6L295aSx6LSlPC90ZXh0Pjwvc3ZnPg==";
        }
      },
      // 加载视频数据
      async loadVideos() {
        try {
          const videos = await this.dbParser.queryVideos();
          this.videoList = videos.map((video) => ({
            ...video,
            displayName: video.original_filename || this.getFilenameFromPath(video.m3u8_path)
          }));
          formatAppLog("log", "at pages/browser/browser.vue:483", "视频列表加载完成:", this.videoList.length);
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:485", "加载视频数据失败:", error);
          throw error;
        }
      },
      // 加载文件数据
      async loadFiles() {
        try {
          const files = await this.dbParser.queryFiles();
          this.fileList = files.map((file) => ({
            ...file,
            displayName: file.original_filename || this.getFilenameFromPath(file.file_path)
          }));
          formatAppLog("log", "at pages/browser/browser.vue:498", "文件列表加载完成:", this.fileList.length);
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:500", "加载文件数据失败:", error);
          throw error;
        }
      },
      // 从文件路径中提取文件名
      getFilenameFromPath(filePath) {
        if (!filePath)
          return "";
        const parts = filePath.split("/");
        return parts[parts.length - 1];
      },
      // 下载并解密图片
      async downloadAndDecryptImage(image) {
        try {
          const cacheKey = image.file_path;
          if (this.decryptedImageCache.has(cacheKey)) {
            return this.decryptedImageCache.get(cacheKey);
          }
          const imageUrl = await generateFileUrl(
            image.file_path,
            this.serverDomain,
            this.accessKey,
            "file"
          );
          const response = await fetch(imageUrl);
          if (!response.ok) {
            throw new Error(`下载失败: ${response.status}`);
          }
          const encryptedData = await response.arrayBuffer();
          const encryptedHex = Array.from(new Uint8Array(encryptedData)).map((b) => b.toString(16).padStart(2, "0")).join("");
          const decryptedData = await decryptAES256GCM(
            encryptedHex,
            image.password,
            image.iv
          );
          const mimeType = getMimeType(image.file_path);
          const blobUrl = createBlobUrl(decryptedData, mimeType);
          this.decryptedImageCache.set(cacheKey, blobUrl);
          return blobUrl;
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:556", "下载解密图片失败:", error);
          throw error;
        }
      },
      // 获取当前列表
      getCurrentList() {
        switch (this.activeTab) {
          case "images":
            return this.imageList;
          case "videos":
            return this.videoList;
          case "files":
            return this.fileList;
          default:
            return [];
        }
      },
      // 格式化日期
      formatDate(dateStr) {
        if (!dateStr)
          return "";
        const date = new Date(dateStr);
        return date.toLocaleDateString();
      },
      // 图片预览
      async previewImage(image, index) {
        try {
          uni.showLoading({
            title: "加载中..."
          });
          const decryptedUrl = await this.downloadAndDecryptImage(image);
          uni.previewImage({
            urls: [decryptedUrl],
            current: 0
          });
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:594", "预览图片失败:", error);
          uni.showToast({
            title: "预览失败",
            icon: "none"
          });
        } finally {
          uni.hideLoading();
        }
      },
      // 播放视频
      async playVideo(video) {
        try {
          uni.showToast({
            title: "视频播放功能开发中",
            icon: "none"
          });
          formatAppLog("log", "at pages/browser/browser.vue:611", "播放视频:", video);
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:613", "播放视频失败:", error);
        }
      },
      // 下载文件
      async downloadFile(file) {
        try {
          uni.showToast({
            title: "文件下载功能开发中",
            icon: "none"
          });
          formatAppLog("log", "at pages/browser/browser.vue:624", "下载文件:", file);
        } catch (error) {
          formatAppLog("error", "at pages/browser/browser.vue:626", "下载文件失败:", error);
        }
      },
      // 图片加载错误
      onImageError(e) {
        formatAppLog("error", "at pages/browser/browser.vue:632", "图片加载失败:", e);
      },
      // 图片加载成功
      onImageLoad(e) {
        formatAppLog("log", "at pages/browser/browser.vue:637", "图片加载成功:", e);
      }
    }
  };
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "container" }, [
      vue.createCommentVNode(" 顶部切换标签 "),
      vue.createElementVNode("view", { class: "tab-bar" }, [
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { active: $data.activeTab === "images" }]),
            onClick: _cache[0] || (_cache[0] = ($event) => $options.switchTab("images"))
          },
          " 图片 ",
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { active: $data.activeTab === "videos" }]),
            onClick: _cache[1] || (_cache[1] = ($event) => $options.switchTab("videos"))
          },
          " 视频 ",
          2
          /* CLASS */
        ),
        vue.createElementVNode(
          "view",
          {
            class: vue.normalizeClass(["tab-item", { active: $data.activeTab === "files" }]),
            onClick: _cache[2] || (_cache[2] = ($event) => $options.switchTab("files"))
          },
          " 文件 ",
          2
          /* CLASS */
        )
      ]),
      vue.createCommentVNode(" 数据库加载 "),
      !$data.dbLoaded ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "db-selector"
      }, [
        vue.createElementVNode("view", { class: "parser-option" }, [
          vue.createElementVNode("label", null, [
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "radio",
                "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $data.useRealParser = $event),
                value: false
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelRadio, $data.useRealParser]
            ]),
            vue.createTextVNode(" 使用模拟数据（快速测试） ")
          ]),
          vue.createElementVNode("label", null, [
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                type: "radio",
                "onUpdate:modelValue": _cache[4] || (_cache[4] = ($event) => $data.useRealParser = $event),
                value: true
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelRadio, $data.useRealParser]
            ]),
            vue.createTextVNode(" 使用真实SQLite解析器 ")
          ])
        ]),
        vue.createElementVNode("button", {
          onClick: _cache[5] || (_cache[5] = (...args) => $options.loadDatabaseFromPrivateDir && $options.loadDatabaseFromPrivateDir(...args)),
          class: "select-btn",
          disabled: $data.loading
        }, vue.toDisplayString($data.loading ? "加载中..." : "加载数据库"), 9, ["disabled"]),
        vue.createElementVNode("view", { class: "tips-container" }, [
          vue.createElementVNode("text", { class: "tip" }, "将从应用私有目录 image_encryption.db 加载数据库"),
          $data.useRealParser ? (vue.openBlock(), vue.createElementBlock("text", {
            key: 0,
            class: "tip"
          }, "注意：真实解析器需要加载sql.js库，首次使用可能较慢")) : vue.createCommentVNode("v-if", true)
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 设置区域 "),
      $data.dbLoaded && !$data.settingsConfigured ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "settings"
      }, [
        vue.createElementVNode("view", { class: "setting-item" }, [
          vue.createElementVNode("text", { class: "label" }, "服务器域名:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              "onUpdate:modelValue": _cache[6] || (_cache[6] = ($event) => $data.serverDomain = $event),
              placeholder: "http://example.com",
              class: "input"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.serverDomain]
          ])
        ]),
        vue.createElementVNode("view", { class: "setting-item" }, [
          vue.createElementVNode("text", { class: "label" }, "访问密钥:"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              "onUpdate:modelValue": _cache[7] || (_cache[7] = ($event) => $data.accessKey = $event),
              placeholder: "访问密钥",
              class: "input"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.accessKey]
          ])
        ]),
        vue.createElementVNode("button", {
          onClick: _cache[8] || (_cache[8] = (...args) => $options.saveSettings && $options.saveSettings(...args)),
          class: "save-btn"
        }, "保存设置")
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 内容区域 "),
      $data.settingsConfigured ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 2,
        class: "content"
      }, [
        vue.createCommentVNode(" 图片列表 "),
        $data.activeTab === "images" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "image-grid"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.imageList, (image, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                key: image.id,
                class: "image-item",
                onClick: ($event) => $options.previewImage(image, index)
              }, [
                vue.createElementVNode("view", { class: "thumbnail-container" }, [
                  vue.createElementVNode("image", {
                    src: image.thumbnailUrl,
                    class: "thumbnail",
                    mode: "aspectFill",
                    onError: _cache[9] || (_cache[9] = (...args) => $options.onImageError && $options.onImageError(...args)),
                    onLoad: _cache[10] || (_cache[10] = (...args) => $options.onImageLoad && $options.onImageLoad(...args))
                  }, null, 40, ["src"]),
                  image.loading ? (vue.openBlock(), vue.createElementBlock("view", {
                    key: 0,
                    class: "loading-overlay"
                  }, [
                    vue.createElementVNode("text", { class: "loading-text" }, "加载中...")
                  ])) : vue.createCommentVNode("v-if", true)
                ]),
                vue.createElementVNode("view", { class: "image-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "filename" },
                    vue.toDisplayString(image.original_filename || "未知文件名"),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "date" },
                    vue.toDisplayString($options.formatDate(image.created_at)),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "size" },
                    vue.toDisplayString(image.file_size_mb) + "MB",
                    1
                    /* TEXT */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 视频列表 "),
        $data.activeTab === "videos" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "video-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.videoList, (video) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                key: video.id,
                class: "video-item",
                onClick: ($event) => $options.playVideo(video)
              }, [
                vue.createElementVNode("view", { class: "video-thumbnail" }, [
                  vue.createElementVNode("text", { class: "play-icon" }, "▶")
                ]),
                vue.createElementVNode("view", { class: "video-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "filename" },
                    vue.toDisplayString(video.displayName),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "date" },
                    vue.toDisplayString($options.formatDate(video.encryption_date)),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "size" },
                    vue.toDisplayString(video.file_size_mb) + "MB",
                    1
                    /* TEXT */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 文件列表 "),
        $data.activeTab === "files" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "file-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($data.fileList, (file) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                key: file.id,
                class: "file-item",
                onClick: ($event) => $options.downloadFile(file)
              }, [
                vue.createElementVNode("view", { class: "file-icon" }, "📄"),
                vue.createElementVNode("view", { class: "file-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "filename" },
                    vue.toDisplayString(file.displayName),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "date" },
                    vue.toDisplayString($options.formatDate(file.encryption_date)),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "size" },
                    vue.toDisplayString(file.file_size_mb) + "MB",
                    1
                    /* TEXT */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 加载状态 "),
        $data.loading ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 3,
          class: "loading"
        }, [
          vue.createElementVNode("text", null, "加载中...")
        ])) : vue.createCommentVNode("v-if", true),
        vue.createCommentVNode(" 空状态 "),
        !$data.loading && $options.getCurrentList().length === 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 4,
          class: "empty"
        }, [
          vue.createElementVNode("text", null, "暂无数据")
        ])) : vue.createCommentVNode("v-if", true)
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesBrowserBrowser = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render$1], ["__scopeId", "data-v-0c8400ea"], ["__file", "D:/phpStudy/WWW/jiami-app/pages/browser/browser.vue"]]);
  __definePage("pages/gallery/gallery", PagesGalleryGallery);
  __definePage("pages/index/index", PagesIndexIndex);
  __definePage("pages/webdav/webdav", PagesWebdavWebdav);
  __definePage("pages/browser/browser", PagesBrowserBrowser);
  const _sfc_main = {
    name: "App",
    onLaunch: function() {
      formatAppLog("log", "at App.vue:11", "App Launch");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:14", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:17", "App Hide");
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { id: "app" }, [
      vue.createCommentVNode(" 应用主体内容 ")
    ]);
  }
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render], ["__file", "D:/phpStudy/WWW/jiami-app/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
