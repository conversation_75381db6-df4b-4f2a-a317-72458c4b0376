<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL.js SQLite解析器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        .loading {
            color: #007AFF;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .file-path {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <h1>SQL.js SQLite解析器测试</h1>
    
    <div class="test-section">
        <h3>1. 加载sql.js库</h3>
        <button onclick="loadSQLJS()" id="loadBtn">加载sql.js</button>
        <div id="loadResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 选择SQLite数据库文件</h3>
        <input type="file" id="dbFile" accept=".db" onchange="loadDatabase()" />
        <div id="dbResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 查询数据库内容</h3>
        <button onclick="queryImages()" disabled id="queryImagesBtn">查询图片</button>
        <button onclick="queryVideos()" disabled id="queryVideosBtn">查询视频</button>
        <button onclick="queryFiles()" disabled id="queryFilesBtn">查询文件</button>
        <button onclick="getStatistics()" disabled id="statsBtn">获取统计</button>
        <div id="queryResult" class="result"></div>
    </div>

    <!-- 引入sql.js库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>
    
    <script type="module">
        import { SQLiteRealParser } from './utils/sqlite-real.js';
        
        let parser = null;
        let sqlLoaded = false;

        window.loadSQLJS = async function() {
            const btn = document.getElementById('loadBtn');
            const result = document.getElementById('loadResult');
            
            btn.disabled = true;
            result.innerHTML = '<div class="loading">正在加载sql.js库...</div>';
            
            try {
                // 检查sql.js是否已加载
                if (typeof window.initSqlJs === 'undefined') {
                    throw new Error('sql.js库未正确加载');
                }
                
                // 初始化sql.js
                const SQL = await window.initSqlJs({
                    locateFile: file => `https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/${file}`
                });
                
                sqlLoaded = true;
                result.innerHTML = '<div class="success">sql.js库加载成功！</div>';
                
                // 创建解析器实例
                parser = new SQLiteRealParser();
                
            } catch (error) {
                result.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
            }
        };

        window.loadDatabase = async function() {
            const fileInput = document.getElementById('dbFile');
            const result = document.getElementById('dbResult');
            
            if (!sqlLoaded) {
                result.innerHTML = '<div class="error">请先加载sql.js库</div>';
                return;
            }
            
            if (!fileInput.files[0]) {
                result.innerHTML = '<div class="error">请选择数据库文件</div>';
                return;
            }
            
            result.innerHTML = '<div class="loading">正在加载数据库...</div>';
            
            try {
                await parser.loadFile(fileInput.files[0]);
                
                result.innerHTML = '<div class="success">数据库加载成功！</div>';
                
                // 启用查询按钮
                document.getElementById('queryImagesBtn').disabled = false;
                document.getElementById('queryVideosBtn').disabled = false;
                document.getElementById('queryFilesBtn').disabled = false;
                document.getElementById('statsBtn').disabled = false;
                
            } catch (error) {
                result.innerHTML = `<div class="error">数据库加载失败: ${error.message}</div>`;
            }
        };

        window.queryImages = async function() {
            const result = document.getElementById('queryResult');
            result.innerHTML = '<div class="loading">正在查询图片...</div>';
            
            try {
                const images = await parser.queryImages();
                
                if (images.length === 0) {
                    result.innerHTML = '<div>没有找到图片数据</div>';
                    return;
                }
                
                let html = `<h4>图片列表 (${images.length}条)</h4>`;
                html += '<table><tr><th>ID</th><th>文件路径</th><th>创建时间</th><th>大小(MB)</th><th>尺寸</th></tr>';
                
                images.forEach(img => {
                    html += `<tr>
                        <td>${img.id}</td>
                        <td class="file-path" title="${img.file_path}">${img.file_path}</td>
                        <td>${img.created_at}</td>
                        <td>${img.file_size_mb || 'N/A'}</td>
                        <td>${img.image_width && img.image_height ? `${img.image_width}x${img.image_height}` : 'N/A'}</td>
                    </tr>`;
                });
                
                html += '</table>';
                result.innerHTML = html;
                
            } catch (error) {
                result.innerHTML = `<div class="error">查询失败: ${error.message}</div>`;
            }
        };

        window.queryVideos = async function() {
            const result = document.getElementById('queryResult');
            result.innerHTML = '<div class="loading">正在查询视频...</div>';
            
            try {
                const videos = await parser.queryVideos();
                
                if (videos.length === 0) {
                    result.innerHTML = '<div>没有找到视频数据</div>';
                    return;
                }
                
                let html = `<h4>视频列表 (${videos.length}条)</h4>`;
                html += '<table><tr><th>ID</th><th>文件名</th><th>M3U8路径</th><th>加密时间</th><th>大小(MB)</th></tr>';
                
                videos.forEach(video => {
                    html += `<tr>
                        <td>${video.id}</td>
                        <td>${video.original_filename}</td>
                        <td class="file-path" title="${video.m3u8_path}">${video.m3u8_path}</td>
                        <td>${video.encryption_date}</td>
                        <td>${video.file_size_mb || 'N/A'}</td>
                    </tr>`;
                });
                
                html += '</table>';
                result.innerHTML = html;
                
            } catch (error) {
                result.innerHTML = `<div class="error">查询失败: ${error.message}</div>`;
            }
        };

        window.queryFiles = async function() {
            const result = document.getElementById('queryResult');
            result.innerHTML = '<div class="loading">正在查询文件...</div>';
            
            try {
                const files = await parser.queryFiles();
                
                if (files.length === 0) {
                    result.innerHTML = '<div>没有找到文件数据</div>';
                    return;
                }
                
                let html = `<h4>文件列表 (${files.length}条)</h4>`;
                html += '<table><tr><th>ID</th><th>文件名</th><th>文件路径</th><th>加密时间</th><th>大小(MB)</th></tr>';
                
                files.forEach(file => {
                    html += `<tr>
                        <td>${file.id}</td>
                        <td>${file.original_filename}</td>
                        <td class="file-path" title="${file.file_path}">${file.file_path}</td>
                        <td>${file.encryption_date}</td>
                        <td>${file.file_size_mb || 'N/A'}</td>
                    </tr>`;
                });
                
                html += '</table>';
                result.innerHTML = html;
                
            } catch (error) {
                result.innerHTML = `<div class="error">查询失败: ${error.message}</div>`;
            }
        };

        window.getStatistics = async function() {
            const result = document.getElementById('queryResult');
            result.innerHTML = '<div class="loading">正在获取统计信息...</div>';
            
            try {
                const stats = await parser.getStatistics();
                
                let html = '<h4>数据库统计</h4>';
                html += `<p><strong>图片数量:</strong> ${stats.imageCount}</p>`;
                html += `<p><strong>视频数量:</strong> ${stats.videoCount}</p>`;
                html += `<p><strong>文件数量:</strong> ${stats.fileCount}</p>`;
                html += `<p><strong>总文件数:</strong> ${stats.totalCount}</p>`;
                html += `<p><strong>总大小:</strong> ${stats.totalSizeMB.toFixed(2)} MB</p>`;
                
                result.innerHTML = html;
                
            } catch (error) {
                result.innerHTML = `<div class="error">获取统计失败: ${error.message}</div>`;
            }
        };

        // 页面加载完成后自动加载sql.js
        window.addEventListener('load', function() {
            console.log('页面加载完成，准备加载sql.js');
            setTimeout(() => {
                loadSQLJS();
            }, 1000);
        });
    </script>
</body>
</html>
