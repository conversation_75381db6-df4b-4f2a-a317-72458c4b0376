<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密工具测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            word-break: break-all;
        }
        button {
            background-color: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>加密工具测试页面</h1>
    
    <div class="test-section">
        <h3>SHA256哈希测试</h3>
        <input type="text" id="hashInput" placeholder="输入要哈希的文本" value="test123">
        <button onclick="testSHA256()">生成SHA256</button>
        <div class="result" id="hashResult"></div>
    </div>

    <div class="test-section">
        <h3>URL生成测试</h3>
        <input type="text" id="domainInput" placeholder="服务器域名" value="http://example.com">
        <input type="text" id="keyInput" placeholder="访问密钥" value="mySecretKey">
        <input type="text" id="pathInput" placeholder="文件路径" value="../data/file/2025-07-31/IMG20190621141002.jpg">
        <button onclick="testUrlGeneration()">生成URL</button>
        <div class="result" id="urlResult"></div>
    </div>

    <div class="test-section">
        <h3>SQLite解析器测试</h3>
        <button onclick="testSQLiteParser()">测试SQLite解析器</button>
        <div class="result" id="sqliteResult"></div>
    </div>

    <script type="module">
        import { generateSHA256, generateFileUrl, generateThumbnailUrl, getTodayDateString } from './utils/crypto-utils.js';
        import { SQLiteParser } from './utils/sqlite-parser.js';

        window.testSHA256 = async function() {
            const input = document.getElementById('hashInput').value;
            const result = document.getElementById('hashResult');
            
            try {
                const hash = await generateSHA256(input);
                result.innerHTML = `<strong>输入:</strong> ${input}<br><strong>SHA256:</strong> ${hash}`;
            } catch (error) {
                result.innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        };

        window.testUrlGeneration = async function() {
            const domain = document.getElementById('domainInput').value;
            const key = document.getElementById('keyInput').value;
            const path = document.getElementById('pathInput').value;
            const result = document.getElementById('urlResult');
            
            try {
                const fileUrl = await generateFileUrl(path, domain, key, 'file');
                const thumbnailUrl = await generateThumbnailUrl(path, domain, key);
                const today = getTodayDateString();
                
                result.innerHTML = `
                    <strong>今日日期:</strong> ${today}<br>
                    <strong>文件URL:</strong> ${fileUrl}<br>
                    <strong>缩略图URL:</strong> ${thumbnailUrl}
                `;
            } catch (error) {
                result.innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        };

        window.testSQLiteParser = async function() {
            const result = document.getElementById('sqliteResult');
            
            try {
                const parser = new SQLiteParser();
                // 模拟加载（实际使用时需要真实的数据库文件）
                parser.buffer = new ArrayBuffer(1024);
                parser.view = new DataView(parser.buffer);
                
                const images = await parser.queryImages();
                const videos = await parser.queryVideos();
                const files = await parser.queryFiles();
                
                result.innerHTML = `
                    <strong>图片数量:</strong> ${images.length}<br>
                    <strong>视频数量:</strong> ${videos.length}<br>
                    <strong>文件数量:</strong> ${files.length}<br>
                    <strong>第一张图片:</strong> ${images[0] ? images[0].file_path : '无'}
                `;
            } catch (error) {
                result.innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        };

        // 页面加载完成后自动运行一些测试
        window.addEventListener('load', function() {
            console.log('测试页面加载完成');
            testSHA256();
        });
    </script>
</body>
</html>
