# 文件浏览器功能说明

## 功能概述

新增的文件浏览器页面提供了一个原生的图片/视频/文件列表浏览功能，支持从SQLite数据库读取加密文件信息并显示解密后的缩略图。

## 主要特性

### 1. 三种文件类型支持
- **图片列表**: 显示加密图片的缩略图网格
- **视频列表**: 显示视频文件列表（播放功能开发中）
- **文件列表**: 显示其他类型文件列表（下载功能开发中）

### 2. 数据库支持
- 读取SQLite数据库文件 (`image_encryption.db`)
- 解析三个主要表：
  - `encrypted_images`: 加密图片信息
  - `encrypted_videos`: 加密视频信息  
  - `encrypted_files`: 加密文件信息

### 3. 加密解密
- 使用AES-256-GCM算法解密文件
- 支持WebCrypto API进行客户端解密
- 自动生成访问URL（包含SHA256哈希验证）

## 使用方法

### 1. 配置设置
首次使用需要配置：
- **服务器域名**: 文件服务器的完整域名（如：http://example.com）
- **访问密钥**: 用于生成访问哈希的密钥字符串

### 2. 选择数据库
- 点击"选择数据库文件"按钮
- 选择应用私有目录中`doc`文件夹下的`image_encryption.db`文件

### 3. 浏览文件
- 使用顶部标签切换不同文件类型
- 图片支持点击预览（自动解密显示）
- 视频和文件功能正在开发中

## 技术实现

### URL生成规则
文件访问URL格式：
```
{域名}/{类型}/{相对路径}/{哈希值}/{文件名}
```

其中：
- `类型`: file（图片）、video（视频）、other（文件）
- `哈希值`: SHA256(访问密钥 + 今日日期)
- `今日日期`: YYYY-MM-DD格式

### 缩略图URL
缩略图URL在原文件名基础上添加`_tagimg`后缀：
```
原文件: IMG20190621141002.jpg
缩略图: IMG20190621141002_tagimg.jpg
```

### 解密流程
1. 根据数据库信息生成文件访问URL
2. 下载加密的文件数据
3. 使用数据库中的密码和IV进行AES-256-GCM解密
4. 创建Blob URL用于显示

## 文件结构

```
jiami-app/
├── pages/browser/
│   └── browser.vue          # 主页面组件
├── utils/
│   ├── crypto-utils.js      # 加密解密工具
│   └── sqlite-parser.js     # SQLite解析器
├── test-crypto.html         # 功能测试页面
└── BROWSER_README.md        # 本说明文件
```

## 注意事项

1. **安全性**: 所有解密操作在客户端进行，密钥不会发送到服务器
2. **性能**: 缩略图采用异步加载，避免阻塞界面
3. **缓存**: 解密后的图片会缓存在内存中，避免重复解密
4. **兼容性**: 使用WebCrypto API，需要HTTPS环境或localhost

## 开发状态

- ✅ 图片列表和缩略图显示
- ✅ 图片预览功能
- ✅ SQLite数据库解析（模拟数据）
- ✅ 真实SQLite数据库解析（使用sql.js）
- ✅ AES-256-GCM解密
- ✅ 底部导航栏集成
- ✅ 设置保存和加载
- ⏳ 视频播放功能（开发中）
- ⏳ 文件下载功能（开发中）

## 测试

### 1. 基础功能测试
```bash
# 在浏览器中打开
file:///path/to/jiami-app/test-crypto.html
```

测试包括：
- SHA256哈希生成
- URL生成逻辑
- SQLite解析器基础功能

### 2. 真实SQLite解析器测试
```bash
# 在浏览器中打开
file:///path/to/jiami-app/test-sqljs.html
```

测试包括：
- sql.js库加载
- 真实SQLite数据库文件解析
- 数据库表查询
- 统计信息获取

## 部署说明

### 在uniapp中使用
1. 将所有文件复制到uniapp项目中
2. 在pages.json中已配置好路由和底部导航
3. 直接运行项目即可使用

### 在Web环境中使用真实SQLite解析器
1. 确保能访问CDN上的sql.js库
2. 或下载sql.js文件到本地并修改路径
3. 在HTTPS环境或localhost中运行（WebCrypto API要求）

## 性能优化建议

1. **缓存策略**: 解密后的图片已实现内存缓存
2. **懒加载**: 缩略图采用异步加载，避免阻塞
3. **分页加载**: 对于大量图片，建议实现分页或虚拟滚动
4. **Web Worker**: 可将解密操作移到Web Worker中执行
